/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #ffffff;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    min-height: 100vh;
}

/* Navigation Styles */
.navbar {
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    font-size: 1.25rem;
}

.logo-icon-img {
    width: 32px;
    height: 32px;
    object-fit: contain;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
    position:relative;
}

.weather-link i {
  margin-right: 6px;
  color: #f1c40f;
}

.nav-link {
    color: #ffffff;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #f1c40f;
}

.nav-link.activ::after::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 100%;
    height: 2px;
    background: #f1c40f;
}

.btn-primary {
    background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);
    color: #2c3e50;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(241, 196, 15, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(241, 196, 15, 0.4);
}

.btn-secondary {
    background: transparent;
    color: #f1c40f;
    border: 2px solid #f1c40f;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #f1c40f;
    color: #2c3e50;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(241, 196, 15, 0.4);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 2px;
    background: #ffffff;
    transition: 0.3s;
}

/* Main Content */
.main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f1c40f 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    font-weight: 300;
    margin-bottom: 3rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Search Section */
.search-section {
    max-width: 600px;
    margin: 0 auto;
}

.search-container {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.city-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 1rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.city-input:focus {
    outline: none;
    border-color: #f1c40f;
    box-shadow: 0 0 0 3px rgba(241, 196, 15, 0.2);
}

.city-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.search-btn {
    background: linear-gradient(135deg, #f1c40f 0%, #f39c12 100%);
    color: #2c3e50;
    border: none;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(241, 196, 15, 0.3);
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(241, 196, 15, 0.4);
}

.search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading and Error States */
.loading-state, .error-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 15px;
    margin-top: 1rem;
}

.loading-state {
    background: rgba(52, 152, 219, 0.2);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.error-state {
    background: rgba(231, 76, 60, 0.2);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

/* Weather Results */
.weather-results {
    padding: 3rem 0;
}

.results-container {
    display: grid;
    gap: 2rem;
}

/* Current Weather Card */
.current-weather-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 2.5rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.current-weather-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.current-weather-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
}

.current-weather-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.current-temp {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.weather-icon-large {
    font-size: 4rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.temperature {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1;
}

.condition {
    font-size: 1.1rem;
    opacity: 0.8;
    font-weight: 500;
}

.current-details {
    display: grid;
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-label {
    opacity: 0.8;
}

.detail-value {
    font-weight: 600;
}

/* Event Recommendation */
.event-recommendation {
    background: linear-gradient(135deg, rgba(241, 196, 15, 0.15) 0%, rgba(243, 156, 18, 0.15) 100%);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(241, 196, 15, 0.3);
    border-radius: 25px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(241, 196, 15, 0.1);
}

.recommendation-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.recommendation-icon {
    font-size: 1.5rem;
}

.recommendation-header h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #f1c40f;
}

.recommendation-content p {
    margin-bottom: 0.75rem;
    line-height: 1.7;
}

.recommendation-content p:first-child {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Forecast Section */
.forecast-section {
    margin-top: 1rem;
}

.forecast-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 2rem;
    text-align: center;
}

.forecast-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

/* Forecast Card */
.forecast-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.forecast-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(241, 196, 15, 0.5);
}

.forecast-card.best-day {
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.2) 0%, rgba(39, 174, 96, 0.2) 100%);
    border: 2px solid #2ecc71;
    box-shadow: 0 10px 30px rgba(46, 204, 113, 0.3);
}

.forecast-card.best-day::after {
    content: '⭐ Best for Events';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #2ecc71;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 600;
}

.forecast-card {
    position: relative;
}

.forecast-day {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.75rem;
    opacity: 0.9;
}

.forecast-icon {
    font-size: 2.5rem;
    margin: 0.75rem 0;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.forecast-temps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.temp-high {
    font-size: 1.25rem;
    font-weight: 700;
}

.temp-low {
    font-size: 1rem;
    opacity: 0.7;
}

.forecast-condition {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

/* Footer */
.footer {
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 4rem;
    padding: 2rem 0 1rem;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 2rem;
    align-items: center;
}

.footer-brand {
    text-align: left;
}

.footer-logo-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-logo-img {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.footer-logo {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.footer-link {
    color: #ffffff;
    text-decoration: none;
    opacity: 0.8;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.footer-link:hover {
    opacity: 1;
    color: #f1c40f;
}

.footer-social {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.social-link {
    color: #ffffff;
    opacity: 0.8;
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.social-link:hover {
    opacity: 1;
    color: #f1c40f;
    background: rgba(241, 196, 15, 0.2);
    transform: translateY(-2px);
}

.footer-bottom {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem 0;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 1.5rem;
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .search-container {
        flex-direction: column;
    }
    
    .search-btn {
        justify-content: center;
    }
    
    .current-weather-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .current-temp {
        justify-content: center;
    }
    
    .forecast-container {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
    }
    
    .current-weather-card,
    .event-recommendation {
        padding: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }
    
    .footer-links {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .footer-social {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .forecast-container {
        grid-template-columns: 1fr;
    }
    
    .nav-container {
        padding: 0 1rem;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for keyboard navigation */
.city-input:focus,
.search-btn:focus,
.nav-link:focus,
.btn-primary:focus,
.btn-secondary:focus,
.forecast-card:focus {
    outline: 2px solid #f1c40f;
    outline-offset: 2px;
}

.forecast-card {
    tabindex: 0;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .current-weather-card,
    .event-recommendation,
    .forecast-card {
        border: 2px solid #ffffff;
    }
}