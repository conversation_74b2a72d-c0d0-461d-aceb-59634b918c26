/* Reset and base styles */
@tailwind base;
@tailwind components;
@tailwind utilities;
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  transition: background-color var(--transition-speed) ease, 
              color var(--transition-speed) ease,
              border-color var(--transition-speed) ease;
}

:root {
  --primary: #4a80f0;
  --primary-rgb: 74, 128, 240;
  --primary-light: #6a9aff;
  --primary-dark: #3a6ad0;
  --secondary: #f0567a;
  --secondary-dark: #27ae60;
  --accent: #f0a04a;
  --accent-dark: #c0392b;
  --text: #333333;
  --text-light: #666666;
  --background: #ffffff;
  --contrast1: #96beeefd;
  --contrast2: #d5e9ffc1;
  --contrast3: #e9f3faff;
  --background-alt: #f5f7fa;
  --border: #e0e0e0;
  --shadow: rgba(0, 0, 0, 0.1);
  --success: #4caf50;
  --error: #f44336;
  --warning: #ff9800;
  --info: #2196f3;
  
  /* Transitions for theme switching */
  --transition-speed: 0.3s;
  
  /* Navbar variables */
  --navbar-height: 60px;
  --navbar-height-scrolled: 60px;
  --navbar-bg-light: rgba(255, 255, 255, 0.75);
  --navbar-bg-dark: rgba(18, 18, 24, 0.75);
  --navbar-blur: 8px;
}

[data-theme="dark"] {
  --primary: #5a90ff;
  --primary-rgb: 90, 144, 255;
  --primary-light: #7aa5ff;
  --primary-dark: #4a80f0;
  --secondary: #ff6b8b;
  --accent: #ffb05a;
  --text: #f0f0f0;
  --text-light: #b0b0b0;
  --background: #121212;
  --contrast1: #121212;
  --contrast2: #121212;
  --contrast3: #121212;
  --background-alt: #1e1e2e;
  --border: #2a2a3a;
  --shadow: rgba(0, 0, 0, 0.3);
  --success: #66bb6a;
  --error: #f55a4e;
  --warning: #ffb74d;
  --info: #42a5f5;
}

html,
body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  color: var(--text);
  background-color: var(--background);
  scroll-behavior: smooth;
  line-height: 1.5;
}

body {
  overflow-x: hidden;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  cursor: pointer;
  font-family: inherit;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5em;
}

p {
  margin-bottom: 1rem;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Navbar specific global styles */
.navbar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  z-index: 1000 !important;
  will-change: transform;
  backface-visibility: hidden;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

.navbar-blur {
  backdrop-filter: blur(var(--navbar-blur)) !important;
  -webkit-backdrop-filter: blur(var(--navbar-blur)) !important;
}

.navbar-visible {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1.25rem;
  font-weight: 500;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary);
  color: white;
}

/* Bubble Buttons */
.bubble-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.8rem 1.8rem;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.primary-bubble {
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  color: white;
  box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.3);
}

.secondary-bubble {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--text);
  border: 1px solid rgba(var(--primary-rgb), 0.3);
}

.bubble-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%);
  transform: translateX(-100%) rotate(45deg);
  transition: transform 0.6s ease;
  z-index: -1;
}

.bubble-btn:hover {
  transform: translateY(-5px) scale(1.03);
}

.primary-bubble:hover {
  box-shadow: 0 12px 30px rgba(var(--primary-rgb), 0.4);
}

.secondary-bubble:hover {
  box-shadow: 0 12px 30px rgba(var(--primary-rgb), 0.2);
  border-color: rgba(var(--primary-rgb), 0.5);
}

.bubble-btn:hover::before {
  transform: translateX(100%) rotate(45deg);
}

.btn-icon {
  margin-right: 0.8rem;
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.bubble-btn:hover .btn-icon {
  transform: scale(1.2);
}

.btn-text {
  position: relative;
}

.btn-text::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: currentColor;
  transition: width 0.3s ease;
}

.bubble-btn:hover .btn-text::after {
  width: 100%;
}

/* Form elements */
input, 
textarea, 
select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

input:focus, 
textarea:focus, 
select:focus {
  outline: none;
  border-color: var(--primary);
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

/* Grid system */
.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1.5rem;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.mb-1 {
  margin-bottom: 0.5rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.mb-3 {
  margin-bottom: 1.5rem;
}

.mb-4 {
  margin-bottom: 2rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

.mt-1 {
  margin-top: 0.5rem;
}

.mt-2 {
  margin-top: 1rem;
}

.mt-3 {
  margin-top: 1.5rem;
}

.mt-4 {
  margin-top: 2rem;
}

.mt-5 {
  margin-top: 3rem;
}

.py-1 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-2 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-3 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-4 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-5 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.px-1 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-2 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-3 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-4 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-5 {
  padding-left: 3rem;
  padding-right: 3rem;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .hide-mobile {
    display: none;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none;
  }
}

/* Map styles */
.map-container {
  width: 100%;
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
}

/* Leaflet specific styles */
.leaflet-container {
  width: 100%;
  height: 100%;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.leaflet-popup-content {
  margin: 12px 16px;
  line-height: 1.5;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.section {
  padding: 4rem 0;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--text);
}

.section-subtitle {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  text-align: center;
  color: var(--text-light);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

@media (max-width: 768px) {
  .section {
    padding: 3rem 0;
  }
  
  .section-title {
    font-size: 2rem;
  }
}

/* Card styles */
.card {
  background-color: var(--background);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px var(--shadow);
}

/* Form styles */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text);
}

.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border);
  border-radius: 4px;
  font-size: 1rem;
  background-color: var(--background);
  color: var(--text);
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
}

/* Animation keyframes */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
} 

        .gps-button-div{
            position: absolute;
            right: 20px;
            bottom: 35px;
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .gps-button{
            height: 50px;
            width: 50px;
            border-radius: 50%;
            background-color: white;
            border-color: rgb(204, 204, 204);
            border-style: solid;
            color: black;
            cursor: pointer;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .gps-button:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
        }

        .gps-icon{
            width: 80%;
        }

@tailwind base;
@tailwind components;
@tailwind utilities;