* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #f59e0b;
    --secondary-color: #1e293b;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #ffffff;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #475569;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    width: 2.5rem;
    height: 2.5rem;
    background-color: var(--primary-color);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bg-primary);
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.logo p {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.nav {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    transition: color 0.2s ease;
    cursor: pointer;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link.active {
    color: var(--primary-color);
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--bg-primary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 9999px;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    letter-spacing: 0.025em;
}

.btn-primary:hover {
    background-color: #d97706;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary:hover {
    background-color: var(--border-color);
    transform: translateY(-1px);
}

/* Main Content */
.main {
    padding: 2rem 0;
}

.grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

.card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-title svg {
    color: var(--primary-color);
    width: 1.5rem;
    height: 1.5rem;
}

.card-title h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Form Section */
.form-grid {
    padding: 1.5rem;
    display: grid;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.form-group input,
.form-group select {
    padding: 0.75rem 1rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.input-with-icon {
    position: relative;
}

.input-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

.input-with-icon input {
    padding-left: 2.75rem;
}

.form-actions {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
}

/* Badge Preview */
.preview-container {
    padding: 2rem;
    display: flex;
    justify-content: center;
}

.badge {
    width: 320px;
    height: 480px;
    border-radius: 1rem;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Badge Themes */
.professional-theme {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
}

.creative-theme {
    background: linear-gradient(135deg, #7c3aed 0%, #ec4899 100%);
    color: white;
}

.minimal-theme {
    background: white;
    color: #1f2937;
    border: 2px solid #e5e7eb;
}

.tech-theme {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: white;
    border: 1px solid #00d9ff;
}

.badge-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.badge-logo svg {
    color: var(--primary-color);
}

.badge-brand {
    font-weight: 700;
    font-size: 1.125rem;
    color: inherit;
}

.badge-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.badge-title {
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.badge-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.badge-detail {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.badge-detail svg {
    flex-shrink: 0;
}

.badge-footer {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
}

.badge-qr {
    width: 3rem;
    height: 3rem;
    border: 2px solid currentColor;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
}

/* Theme Specific Styles */
.creative-theme .badge-logo svg {
    color: #fbbf24;
}

.minimal-theme .badge-logo svg {
    color: #3b82f6;
}

.tech-theme .badge-logo svg {
    color: #00d9ff;
}

.tech-theme .badge-detail svg,
.tech-theme .badge-qr {
    color: #00d9ff;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .preview-section {
        order: -1;
    }
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .nav {
        width: 100%;
        justify-content: space-between;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .badge {
        width: 280px;
        height: 420px;
        padding: 1.5rem;
    }
    
    .badge-title {
        font-size: 1.25rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }
    
    .main {
        padding: 1rem 0;
    }
    
    .badge {
        width: 260px;
        height: 380px;
        padding: 1.25rem;
    }
    
    .preview-container {
        padding: 1rem;
    }
}

/* Loading Animation */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

/* Success Animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.success-pulse {
    animation: pulse 0.6s ease-in-out;
}