"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"\\r\\n/* Reset and base styles */\\r\\n@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n* {\\r\\n  margin: 0;\\r\\n  padding: 0;\\r\\n  box-sizing: border-box;\\r\\n  transition: background-color var(--transition-speed) ease, \\r\\n              color var(--transition-speed) ease,\\r\\n              border-color var(--transition-speed) ease;\\r\\n}\\r\\n\\r\\n:root {\\r\\n  --primary: #4a80f0;\\r\\n  --primary-rgb: 74, 128, 240;\\r\\n  --primary-light: #6a9aff;\\r\\n  --primary-dark: #3a6ad0;\\r\\n  --secondary: #f0567a;\\r\\n  --secondary-dark: #27ae60;\\r\\n  --accent: #f0a04a;\\r\\n  --accent-dark: #c0392b;\\r\\n  --text: #333333;\\r\\n  --text-light: #666666;\\r\\n  --background: #ffffff;\\r\\n  --contrast1: #96beeefd;\\r\\n  --contrast2: #d5e9ffc1;\\r\\n  --contrast3: #e9f3faff;\\r\\n  --background-alt: #f5f7fa;\\r\\n  --border: #e0e0e0;\\r\\n  --shadow: rgba(0, 0, 0, 0.1);\\r\\n  --success: #4caf50;\\r\\n  --error: #f44336;\\r\\n  --warning: #ff9800;\\r\\n  --info: #2196f3;\\r\\n  \\r\\n  /* Transitions for theme switching */\\r\\n  --transition-speed: 0.3s;\\r\\n  \\r\\n  /* Navbar variables */\\r\\n  --navbar-height: 60px;\\r\\n  --navbar-height-scrolled: 60px;\\r\\n  --navbar-bg-light: rgba(255, 255, 255, 0.75);\\r\\n  --navbar-bg-dark: rgba(18, 18, 24, 0.75);\\r\\n  --navbar-blur: 8px;\\r\\n}\\r\\n\\r\\n[data-theme=\\\"dark\\\"] {\\r\\n  --primary: #5a90ff;\\r\\n  --primary-rgb: 90, 144, 255;\\r\\n  --primary-light: #7aa5ff;\\r\\n  --primary-dark: #4a80f0;\\r\\n  --secondary: #ff6b8b;\\r\\n  --accent: #ffb05a;\\r\\n  --text: #f0f0f0;\\r\\n  --text-light: #b0b0b0;\\r\\n  --background: #121212;\\r\\n  --contrast1: #121212;\\r\\n  --contrast2: #121212;\\r\\n  --contrast3: #121212;\\r\\n  --background-alt: #1e1e2e;\\r\\n  --border: #2a2a3a;\\r\\n  --shadow: rgba(0, 0, 0, 0.3);\\r\\n  --success: #66bb6a;\\r\\n  --error: #f55a4e;\\r\\n  --warning: #ffb74d;\\r\\n  --info: #42a5f5;\\r\\n}\\r\\n\\r\\nhtml,\\r\\nbody {\\r\\n  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,\\r\\n    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;\\r\\n  color: var(--text);\\r\\n  background-color: var(--background);\\r\\n  scroll-behavior: smooth;\\r\\n  line-height: 1.5;\\r\\n}\\r\\n\\r\\nbody {\\r\\n  overflow-x: hidden;\\r\\n}\\r\\n\\r\\na {\\r\\n  color: inherit;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\nbutton {\\r\\n  cursor: pointer;\\r\\n  font-family: inherit;\\r\\n}\\r\\n\\r\\nh1, h2, h3, h4, h5, h6 {\\r\\n  font-weight: 600;\\r\\n  line-height: 1.2;\\r\\n  margin-bottom: 0.5em;\\r\\n}\\r\\n\\r\\np {\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\nimg {\\r\\n  max-width: 100%;\\r\\n  height: auto;\\r\\n  display: block;\\r\\n}\\r\\n\\r\\n/* Navbar specific global styles */\\r\\n.navbar {\\r\\n  position: fixed !important;\\r\\n  top: 0 !important;\\r\\n  left: 0 !important;\\r\\n  width: 100% !important;\\r\\n  z-index: 1000 !important;\\r\\n  will-change: transform;\\r\\n  -webkit-backface-visibility: hidden;\\r\\n          backface-visibility: hidden;\\r\\n  transform: translateZ(0);\\r\\n  -webkit-transform: translateZ(0);\\r\\n}\\r\\n\\r\\n.navbar-blur {\\r\\n  backdrop-filter: blur(var(--navbar-blur)) !important;\\r\\n  -webkit-backdrop-filter: blur(var(--navbar-blur)) !important;\\r\\n}\\r\\n\\r\\n.navbar-visible {\\r\\n  opacity: 1 !important;\\r\\n  visibility: visible !important;\\r\\n}\\r\\n\\r\\n/* Container */\\r\\n.container {\\r\\n  width: 100%;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n  padding: 0 1rem;\\r\\n}\\r\\n\\r\\n/* Buttons */\\r\\n.btn {\\r\\n  display: inline-flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 0.5rem 1.25rem;\\r\\n  font-weight: 500;\\r\\n  border-radius: 4px;\\r\\n  border: none;\\r\\n  cursor: pointer;\\r\\n  transition: all 0.2s ease;\\r\\n  font-size: 1rem;\\r\\n}\\r\\n\\r\\n.btn-primary {\\r\\n  background-color: var(--primary);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.btn-primary:hover {\\r\\n  background-color: var(--primary-dark);\\r\\n}\\r\\n\\r\\n.btn-secondary {\\r\\n  background-color: var(--secondary);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.btn-secondary:hover {\\r\\n  background-color: var(--secondary-dark);\\r\\n}\\r\\n\\r\\n.btn-outline {\\r\\n  background-color: transparent;\\r\\n  border: 1px solid var(--primary);\\r\\n  color: var(--primary);\\r\\n}\\r\\n\\r\\n.btn-outline:hover {\\r\\n  background-color: var(--primary);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n/* Bubble Buttons */\\r\\n.bubble-btn {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 0.8rem 1.8rem;\\r\\n  border-radius: 30px;\\r\\n  font-weight: 600;\\r\\n  font-size: 1.1rem;\\r\\n  cursor: pointer;\\r\\n  transition: all 0.3s ease;\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\r\\n  z-index: 1;\\r\\n}\\r\\n\\r\\n.primary-bubble {\\r\\n  background: linear-gradient(135deg, var(--primary), var(--primary-dark));\\r\\n  color: white;\\r\\n  box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.3);\\r\\n}\\r\\n\\r\\n.secondary-bubble {\\r\\n  background: rgba(255, 255, 255, 0.8);\\r\\n  backdrop-filter: blur(10px);\\r\\n  -webkit-backdrop-filter: blur(10px);\\r\\n  color: var(--text);\\r\\n  border: 1px solid rgba(var(--primary-rgb), 0.3);\\r\\n}\\r\\n\\r\\n.bubble-btn::before {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%);\\r\\n  transform: translateX(-100%) rotate(45deg);\\r\\n  transition: transform 0.6s ease;\\r\\n  z-index: -1;\\r\\n}\\r\\n\\r\\n.bubble-btn:hover {\\r\\n  transform: translateY(-5px) scale(1.03);\\r\\n}\\r\\n\\r\\n.primary-bubble:hover {\\r\\n  box-shadow: 0 12px 30px rgba(var(--primary-rgb), 0.4);\\r\\n}\\r\\n\\r\\n.secondary-bubble:hover {\\r\\n  box-shadow: 0 12px 30px rgba(var(--primary-rgb), 0.2);\\r\\n  border-color: rgba(var(--primary-rgb), 0.5);\\r\\n}\\r\\n\\r\\n.bubble-btn:hover::before {\\r\\n  transform: translateX(100%) rotate(45deg);\\r\\n}\\r\\n\\r\\n.btn-icon {\\r\\n  margin-right: 0.8rem;\\r\\n  font-size: 1.2rem;\\r\\n  transition: transform 0.3s ease;\\r\\n}\\r\\n\\r\\n.bubble-btn:hover .btn-icon {\\r\\n  transform: scale(1.2);\\r\\n}\\r\\n\\r\\n.btn-text {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.btn-text::after {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  bottom: -2px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background: currentColor;\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.bubble-btn:hover .btn-text::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Form elements */\\r\\ninput, \\r\\ntextarea, \\r\\nselect {\\r\\n  width: 100%;\\r\\n  padding: 0.75rem;\\r\\n  border: 1px solid var(--border);\\r\\n  border-radius: 4px;\\r\\n  font-family: inherit;\\r\\n  font-size: 1rem;\\r\\n  transition: border-color 0.2s ease;\\r\\n}\\r\\n\\r\\ninput:focus, \\r\\ntextarea:focus, \\r\\nselect:focus {\\r\\n  outline: none;\\r\\n  border-color: var(--primary);\\r\\n}\\r\\n\\r\\nlabel {\\r\\n  display: block;\\r\\n  margin-bottom: 0.5rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n/* Grid system */\\r\\n.grid {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(12, 1fr);\\r\\n  grid-gap: 1.5rem;\\r\\n  gap: 1.5rem;\\r\\n}\\r\\n\\r\\n/* Utility classes */\\r\\n.text-center {\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.mb-1 {\\r\\n  margin-bottom: 0.5rem;\\r\\n}\\r\\n\\r\\n.mb-2 {\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\n.mb-3 {\\r\\n  margin-bottom: 1.5rem;\\r\\n}\\r\\n\\r\\n.mb-4 {\\r\\n  margin-bottom: 2rem;\\r\\n}\\r\\n\\r\\n.mb-5 {\\r\\n  margin-bottom: 3rem;\\r\\n}\\r\\n\\r\\n.mt-1 {\\r\\n  margin-top: 0.5rem;\\r\\n}\\r\\n\\r\\n.mt-2 {\\r\\n  margin-top: 1rem;\\r\\n}\\r\\n\\r\\n.mt-3 {\\r\\n  margin-top: 1.5rem;\\r\\n}\\r\\n\\r\\n.mt-4 {\\r\\n  margin-top: 2rem;\\r\\n}\\r\\n\\r\\n.mt-5 {\\r\\n  margin-top: 3rem;\\r\\n}\\r\\n\\r\\n.py-1 {\\r\\n  padding-top: 0.5rem;\\r\\n  padding-bottom: 0.5rem;\\r\\n}\\r\\n\\r\\n.py-2 {\\r\\n  padding-top: 1rem;\\r\\n  padding-bottom: 1rem;\\r\\n}\\r\\n\\r\\n.py-3 {\\r\\n  padding-top: 1.5rem;\\r\\n  padding-bottom: 1.5rem;\\r\\n}\\r\\n\\r\\n.py-4 {\\r\\n  padding-top: 2rem;\\r\\n  padding-bottom: 2rem;\\r\\n}\\r\\n\\r\\n.py-5 {\\r\\n  padding-top: 3rem;\\r\\n  padding-bottom: 3rem;\\r\\n}\\r\\n\\r\\n.px-1 {\\r\\n  padding-left: 0.5rem;\\r\\n  padding-right: 0.5rem;\\r\\n}\\r\\n\\r\\n.px-2 {\\r\\n  padding-left: 1rem;\\r\\n  padding-right: 1rem;\\r\\n}\\r\\n\\r\\n.px-3 {\\r\\n  padding-left: 1.5rem;\\r\\n  padding-right: 1.5rem;\\r\\n}\\r\\n\\r\\n.px-4 {\\r\\n  padding-left: 2rem;\\r\\n  padding-right: 2rem;\\r\\n}\\r\\n\\r\\n.px-5 {\\r\\n  padding-left: 3rem;\\r\\n  padding-right: 3rem;\\r\\n}\\r\\n\\r\\n/* Responsive utilities */\\r\\n@media (max-width: 768px) {\\r\\n  .hide-mobile {\\r\\n    display: none;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (min-width: 769px) {\\r\\n  .hide-desktop {\\r\\n    display: none;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Map styles */\\r\\n.map-container {\\r\\n  width: 100%;\\r\\n  height: 500px;\\r\\n  border-radius: 8px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n/* Leaflet specific styles */\\r\\n.leaflet-container {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.leaflet-popup-content-wrapper {\\r\\n  border-radius: 8px;\\r\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.leaflet-popup-content {\\r\\n  margin: 12px 16px;\\r\\n  line-height: 1.5;\\r\\n}\\r\\n\\r\\n/* Animation utilities */\\r\\n.fade-in {\\r\\n  animation: fadeIn 0.5s ease-in;\\r\\n}\\r\\n\\r\\n@keyframes fadeIn {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n  }\\r\\n}\\r\\n\\r\\n.slide-in {\\r\\n  animation: slideIn 0.5s ease-out;\\r\\n}\\r\\n\\r\\n@keyframes slideIn {\\r\\n  from {\\r\\n    transform: translateY(20px);\\r\\n    opacity: 0;\\r\\n  }\\r\\n  to {\\r\\n    transform: translateY(0);\\r\\n    opacity: 1;\\r\\n  }\\r\\n}\\r\\n\\r\\n.section {\\r\\n  padding: 4rem 0;\\r\\n}\\r\\n\\r\\n.section-title {\\r\\n  font-size: 2.5rem;\\r\\n  margin-bottom: 2rem;\\r\\n  text-align: center;\\r\\n  color: var(--text);\\r\\n}\\r\\n\\r\\n.section-subtitle {\\r\\n  font-size: 1.2rem;\\r\\n  margin-bottom: 3rem;\\r\\n  text-align: center;\\r\\n  color: var(--text-light);\\r\\n  max-width: 800px;\\r\\n  margin-left: auto;\\r\\n  margin-right: auto;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .section {\\r\\n    padding: 3rem 0;\\r\\n  }\\r\\n  \\r\\n  .section-title {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Card styles */\\r\\n.card {\\r\\n  background-color: var(--background);\\r\\n  border-radius: 8px;\\r\\n  box-shadow: 0 4px 12px var(--shadow);\\r\\n  overflow: hidden;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.card:hover {\\r\\n  transform: translateY(-5px);\\r\\n  box-shadow: 0 8px 24px var(--shadow);\\r\\n}\\r\\n\\r\\n/* Form styles */\\r\\n.form-group {\\r\\n  margin-bottom: 1.5rem;\\r\\n}\\r\\n\\r\\n.form-label {\\r\\n  display: block;\\r\\n  margin-bottom: 0.5rem;\\r\\n  font-weight: 500;\\r\\n  color: var(--text);\\r\\n}\\r\\n\\r\\n.form-input {\\r\\n  width: 100%;\\r\\n  padding: 0.75rem;\\r\\n  border: 1px solid var(--border);\\r\\n  border-radius: 4px;\\r\\n  font-size: 1rem;\\r\\n  background-color: var(--background);\\r\\n  color: var(--text);\\r\\n  transition: border-color 0.2s ease;\\r\\n}\\r\\n\\r\\n.form-input:focus {\\r\\n  outline: none;\\r\\n  border-color: var(--primary);\\r\\n}\\r\\n\\r\\n/* Animation keyframes */\\r\\n@keyframes slideUp {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(20px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse {\\r\\n  0% {\\r\\n    transform: scale(1);\\r\\n    opacity: 0.6;\\r\\n  }\\r\\n  50% {\\r\\n    transform: scale(1.1);\\r\\n    opacity: 0.3;\\r\\n  }\\r\\n  100% {\\r\\n    transform: scale(1);\\r\\n    opacity: 0.6;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-10px);\\r\\n  }\\r\\n  100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n} \\r\\n\\r\\n        .gps-button-div{\\r\\n            position: absolute;\\r\\n            right: 20px;\\r\\n            bottom: 35px;\\r\\n            z-index: 1000;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        .gps-button{\\r\\n            height: 50px;\\r\\n            width: 50px;\\r\\n            border-radius: 50%;\\r\\n            background-color: var(--background);\\r\\n            border-color: var(--border);\\r\\n            border-style: solid;\\r\\n            color: var(--text);\\r\\n            cursor: pointer;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n            box-shadow: 0 2px 6px var(--shadow);\\r\\n            transition: transform 0.2s ease, box-shadow 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .gps-button:hover {\\r\\n          transform: scale(1.1);\\r\\n          box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);\\r\\n        }\\r\\n\\r\\n        .gps-icon{\\r\\n            width: 80%;\\r\\n        }\\r\\n\\r\\n@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\";AACA,0BAA0B;AAC1B,cAAc;AACd,oBAAoB;AACpB,mBAAmB;AACnB;EACE,SAAS;EACT,UAAU;EACV,sBAAsB;EACtB;;uDAEqD;AACvD;;AAEA;EACE,kBAAkB;EAClB,2BAA2B;EAC3B,wBAAwB;EACxB,uBAAuB;EACvB,oBAAoB;EACpB,yBAAyB;EACzB,iBAAiB;EACjB,sBAAsB;EACtB,eAAe;EACf,qBAAqB;EACrB,qBAAqB;EACrB,sBAAsB;EACtB,sBAAsB;EACtB,sBAAsB;EACtB,yBAAyB;EACzB,iBAAiB;EACjB,4BAA4B;EAC5B,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,eAAe;;EAEf,oCAAoC;EACpC,wBAAwB;;EAExB,qBAAqB;EACrB,qBAAqB;EACrB,8BAA8B;EAC9B,4CAA4C;EAC5C,wCAAwC;EACxC,kBAAkB;AACpB;;AAEA;EACE,kBAAkB;EAClB,2BAA2B;EAC3B,wBAAwB;EACxB,uBAAuB;EACvB,oBAAoB;EACpB,iBAAiB;EACjB,eAAe;EACf,qBAAqB;EACrB,qBAAqB;EACrB,oBAAoB;EACpB,oBAAoB;EACpB,oBAAoB;EACpB,yBAAyB;EACzB,iBAAiB;EACjB,4BAA4B;EAC5B,kBAAkB;EAClB,gBAAgB;EAChB,kBAAkB;EAClB,eAAe;AACjB;;AAEA;;EAEE;wEACsE;EACtE,kBAAkB;EAClB,mCAAmC;EACnC,uBAAuB;EACvB,gBAAgB;AAClB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,cAAc;EACd,qBAAqB;AACvB;;AAEA;EACE,eAAe;EACf,oBAAoB;AACtB;;AAEA;EACE,gBAAgB;EAChB,gBAAgB;EAChB,oBAAoB;AACtB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,eAAe;EACf,YAAY;EACZ,cAAc;AAChB;;AAEA,kCAAkC;AAClC;EACE,0BAA0B;EAC1B,iBAAiB;EACjB,kBAAkB;EAClB,sBAAsB;EACtB,wBAAwB;EACxB,sBAAsB;EACtB,mCAA2B;UAA3B,2BAA2B;EAC3B,wBAAwB;EACxB,gCAAgC;AAClC;;AAEA;EACE,oDAAoD;EACpD,4DAA4D;AAC9D;;AAEA;EACE,qBAAqB;EACrB,8BAA8B;AAChC;;AAEA,cAAc;AACd;EACE,WAAW;EACX,iBAAiB;EACjB,cAAc;EACd,eAAe;AACjB;;AAEA,YAAY;AACZ;EACE,oBAAoB;EACpB,mBAAmB;EACnB,uBAAuB;EACvB,uBAAuB;EACvB,gBAAgB;EAChB,kBAAkB;EAClB,YAAY;EACZ,eAAe;EACf,yBAAyB;EACzB,eAAe;AACjB;;AAEA;EACE,gCAAgC;EAChC,YAAY;AACd;;AAEA;EACE,qCAAqC;AACvC;;AAEA;EACE,kCAAkC;EAClC,YAAY;AACd;;AAEA;EACE,uCAAuC;AACzC;;AAEA;EACE,6BAA6B;EAC7B,gCAAgC;EAChC,qBAAqB;AACvB;;AAEA;EACE,gCAAgC;EAChC,YAAY;AACd;;AAEA,mBAAmB;AACnB;EACE,aAAa;EACb,mBAAmB;EACnB,uBAAuB;EACvB,sBAAsB;EACtB,mBAAmB;EACnB,gBAAgB;EAChB,iBAAiB;EACjB,eAAe;EACf,yBAAyB;EACzB,kBAAkB;EAClB,gBAAgB;EAChB,yCAAyC;EACzC,UAAU;AACZ;;AAEA;EACE,wEAAwE;EACxE,YAAY;EACZ,oDAAoD;AACtD;;AAEA;EACE,oCAAoC;EACpC,2BAA2B;EAC3B,mCAAmC;EACnC,kBAAkB;EAClB,+CAA+C;AACjD;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,MAAM;EACN,OAAO;EACP,WAAW;EACX,YAAY;EACZ,sFAAsF;EACtF,0CAA0C;EAC1C,+BAA+B;EAC/B,WAAW;AACb;;AAEA;EACE,uCAAuC;AACzC;;AAEA;EACE,qDAAqD;AACvD;;AAEA;EACE,qDAAqD;EACrD,2CAA2C;AAC7C;;AAEA;EACE,yCAAyC;AAC3C;;AAEA;EACE,oBAAoB;EACpB,iBAAiB;EACjB,+BAA+B;AACjC;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,kBAAkB;EAClB,YAAY;EACZ,OAAO;EACP,QAAQ;EACR,WAAW;EACX,wBAAwB;EACxB,2BAA2B;AAC7B;;AAEA;EACE,WAAW;AACb;;AAEA,kBAAkB;AAClB;;;EAGE,WAAW;EACX,gBAAgB;EAChB,+BAA+B;EAC/B,kBAAkB;EAClB,oBAAoB;EACpB,eAAe;EACf,kCAAkC;AACpC;;AAEA;;;EAGE,aAAa;EACb,4BAA4B;AAC9B;;AAEA;EACE,cAAc;EACd,qBAAqB;EACrB,gBAAgB;AAClB;;AAEA,gBAAgB;AAChB;EACE,aAAa;EACb,sCAAsC;EACtC,gBAAW;EAAX,WAAW;AACb;;AAEA,oBAAoB;AACpB;EACE,kBAAkB;AACpB;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,qBAAqB;AACvB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,kBAAkB;AACpB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,gBAAgB;AAClB;;AAEA;EACE,mBAAmB;EACnB,sBAAsB;AACxB;;AAEA;EACE,iBAAiB;EACjB,oBAAoB;AACtB;;AAEA;EACE,mBAAmB;EACnB,sBAAsB;AACxB;;AAEA;EACE,iBAAiB;EACjB,oBAAoB;AACtB;;AAEA;EACE,iBAAiB;EACjB,oBAAoB;AACtB;;AAEA;EACE,oBAAoB;EACpB,qBAAqB;AACvB;;AAEA;EACE,kBAAkB;EAClB,mBAAmB;AACrB;;AAEA;EACE,oBAAoB;EACpB,qBAAqB;AACvB;;AAEA;EACE,kBAAkB;EAClB,mBAAmB;AACrB;;AAEA;EACE,kBAAkB;EAClB,mBAAmB;AACrB;;AAEA,yBAAyB;AACzB;EACE;IACE,aAAa;EACf;AACF;;AAEA;EACE;IACE,aAAa;EACf;AACF;;AAEA,eAAe;AACf;EACE,WAAW;EACX,aAAa;EACb,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA,4BAA4B;AAC5B;EACE,WAAW;EACX,YAAY;AACd;;AAEA;EACE,kBAAkB;EAClB,yCAAyC;AAC3C;;AAEA;EACE,iBAAiB;EACjB,gBAAgB;AAClB;;AAEA,wBAAwB;AACxB;EACE,8BAA8B;AAChC;;AAEA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;;AAEA;EACE,gCAAgC;AAClC;;AAEA;EACE;IACE,2BAA2B;IAC3B,UAAU;EACZ;EACA;IACE,wBAAwB;IACxB,UAAU;EACZ;AACF;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,iBAAiB;EACjB,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;AACpB;;AAEA;EACE,iBAAiB;EACjB,mBAAmB;EACnB,kBAAkB;EAClB,wBAAwB;EACxB,gBAAgB;EAChB,iBAAiB;EACjB,kBAAkB;AACpB;;AAEA;EACE;IACE,eAAe;EACjB;;EAEA;IACE,eAAe;EACjB;AACF;;AAEA,gBAAgB;AAChB;EACE,mCAAmC;EACnC,kBAAkB;EAClB,oCAAoC;EACpC,gBAAgB;EAChB,qDAAqD;AACvD;;AAEA;EACE,2BAA2B;EAC3B,oCAAoC;AACtC;;AAEA,gBAAgB;AAChB;EACE,qBAAqB;AACvB;;AAEA;EACE,cAAc;EACd,qBAAqB;EACrB,gBAAgB;EAChB,kBAAkB;AACpB;;AAEA;EACE,WAAW;EACX,gBAAgB;EAChB,+BAA+B;EAC/B,kBAAkB;EAClB,eAAe;EACf,mCAAmC;EACnC,kBAAkB;EAClB,kCAAkC;AACpC;;AAEA;EACE,aAAa;EACb,4BAA4B;AAC9B;;AAEA,wBAAwB;AACxB;EACE;IACE,UAAU;IACV,2BAA2B;EAC7B;EACA;IACE,UAAU;IACV,wBAAwB;EAC1B;AACF;;AAEA;EACE;IACE,mBAAmB;IACnB,YAAY;EACd;EACA;IACE,qBAAqB;IACrB,YAAY;EACd;EACA;IACE,mBAAmB;IACnB,YAAY;EACd;AACF;;AAEA;EACE;IACE,0BAA0B;EAC5B;EACA;IACE,4BAA4B;EAC9B;EACA;IACE,0BAA0B;EAC5B;AACF;;QAEQ;YACI,kBAAkB;YAClB,WAAW;YACX,YAAY;YACZ,aAAa;YACb,aAAa;YACb,uBAAuB;YACvB,mBAAmB;QACvB;;QAEA;YACI,YAAY;YACZ,WAAW;YACX,kBAAkB;YAClB,mCAAmC;YACnC,2BAA2B;YAC3B,mBAAmB;YACnB,kBAAkB;YAClB,eAAe;YACf,aAAa;YACb,uBAAuB;YACvB,mBAAmB;YACnB,mCAAmC;YACnC,qDAAqD;QACzD;;QAEA;UACE,qBAAqB;UACrB,yCAAyC;QAC3C;;QAEA;YACI,UAAU;QACd;;AAER,cAAc;AACd,oBAAoB;AACpB,mBAAmB\",\"sourcesContent\":[\"\\r\\n/* Reset and base styles */\\r\\n@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n* {\\r\\n  margin: 0;\\r\\n  padding: 0;\\r\\n  box-sizing: border-box;\\r\\n  transition: background-color var(--transition-speed) ease, \\r\\n              color var(--transition-speed) ease,\\r\\n              border-color var(--transition-speed) ease;\\r\\n}\\r\\n\\r\\n:root {\\r\\n  --primary: #4a80f0;\\r\\n  --primary-rgb: 74, 128, 240;\\r\\n  --primary-light: #6a9aff;\\r\\n  --primary-dark: #3a6ad0;\\r\\n  --secondary: #f0567a;\\r\\n  --secondary-dark: #27ae60;\\r\\n  --accent: #f0a04a;\\r\\n  --accent-dark: #c0392b;\\r\\n  --text: #333333;\\r\\n  --text-light: #666666;\\r\\n  --background: #ffffff;\\r\\n  --contrast1: #96beeefd;\\r\\n  --contrast2: #d5e9ffc1;\\r\\n  --contrast3: #e9f3faff;\\r\\n  --background-alt: #f5f7fa;\\r\\n  --border: #e0e0e0;\\r\\n  --shadow: rgba(0, 0, 0, 0.1);\\r\\n  --success: #4caf50;\\r\\n  --error: #f44336;\\r\\n  --warning: #ff9800;\\r\\n  --info: #2196f3;\\r\\n  \\r\\n  /* Transitions for theme switching */\\r\\n  --transition-speed: 0.3s;\\r\\n  \\r\\n  /* Navbar variables */\\r\\n  --navbar-height: 60px;\\r\\n  --navbar-height-scrolled: 60px;\\r\\n  --navbar-bg-light: rgba(255, 255, 255, 0.75);\\r\\n  --navbar-bg-dark: rgba(18, 18, 24, 0.75);\\r\\n  --navbar-blur: 8px;\\r\\n}\\r\\n\\r\\n[data-theme=\\\"dark\\\"] {\\r\\n  --primary: #5a90ff;\\r\\n  --primary-rgb: 90, 144, 255;\\r\\n  --primary-light: #7aa5ff;\\r\\n  --primary-dark: #4a80f0;\\r\\n  --secondary: #ff6b8b;\\r\\n  --accent: #ffb05a;\\r\\n  --text: #f0f0f0;\\r\\n  --text-light: #b0b0b0;\\r\\n  --background: #121212;\\r\\n  --contrast1: #121212;\\r\\n  --contrast2: #121212;\\r\\n  --contrast3: #121212;\\r\\n  --background-alt: #1e1e2e;\\r\\n  --border: #2a2a3a;\\r\\n  --shadow: rgba(0, 0, 0, 0.3);\\r\\n  --success: #66bb6a;\\r\\n  --error: #f55a4e;\\r\\n  --warning: #ffb74d;\\r\\n  --info: #42a5f5;\\r\\n}\\r\\n\\r\\nhtml,\\r\\nbody {\\r\\n  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,\\r\\n    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;\\r\\n  color: var(--text);\\r\\n  background-color: var(--background);\\r\\n  scroll-behavior: smooth;\\r\\n  line-height: 1.5;\\r\\n}\\r\\n\\r\\nbody {\\r\\n  overflow-x: hidden;\\r\\n}\\r\\n\\r\\na {\\r\\n  color: inherit;\\r\\n  text-decoration: none;\\r\\n}\\r\\n\\r\\nbutton {\\r\\n  cursor: pointer;\\r\\n  font-family: inherit;\\r\\n}\\r\\n\\r\\nh1, h2, h3, h4, h5, h6 {\\r\\n  font-weight: 600;\\r\\n  line-height: 1.2;\\r\\n  margin-bottom: 0.5em;\\r\\n}\\r\\n\\r\\np {\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\nimg {\\r\\n  max-width: 100%;\\r\\n  height: auto;\\r\\n  display: block;\\r\\n}\\r\\n\\r\\n/* Navbar specific global styles */\\r\\n.navbar {\\r\\n  position: fixed !important;\\r\\n  top: 0 !important;\\r\\n  left: 0 !important;\\r\\n  width: 100% !important;\\r\\n  z-index: 1000 !important;\\r\\n  will-change: transform;\\r\\n  backface-visibility: hidden;\\r\\n  transform: translateZ(0);\\r\\n  -webkit-transform: translateZ(0);\\r\\n}\\r\\n\\r\\n.navbar-blur {\\r\\n  backdrop-filter: blur(var(--navbar-blur)) !important;\\r\\n  -webkit-backdrop-filter: blur(var(--navbar-blur)) !important;\\r\\n}\\r\\n\\r\\n.navbar-visible {\\r\\n  opacity: 1 !important;\\r\\n  visibility: visible !important;\\r\\n}\\r\\n\\r\\n/* Container */\\r\\n.container {\\r\\n  width: 100%;\\r\\n  max-width: 1200px;\\r\\n  margin: 0 auto;\\r\\n  padding: 0 1rem;\\r\\n}\\r\\n\\r\\n/* Buttons */\\r\\n.btn {\\r\\n  display: inline-flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 0.5rem 1.25rem;\\r\\n  font-weight: 500;\\r\\n  border-radius: 4px;\\r\\n  border: none;\\r\\n  cursor: pointer;\\r\\n  transition: all 0.2s ease;\\r\\n  font-size: 1rem;\\r\\n}\\r\\n\\r\\n.btn-primary {\\r\\n  background-color: var(--primary);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.btn-primary:hover {\\r\\n  background-color: var(--primary-dark);\\r\\n}\\r\\n\\r\\n.btn-secondary {\\r\\n  background-color: var(--secondary);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n.btn-secondary:hover {\\r\\n  background-color: var(--secondary-dark);\\r\\n}\\r\\n\\r\\n.btn-outline {\\r\\n  background-color: transparent;\\r\\n  border: 1px solid var(--primary);\\r\\n  color: var(--primary);\\r\\n}\\r\\n\\r\\n.btn-outline:hover {\\r\\n  background-color: var(--primary);\\r\\n  color: white;\\r\\n}\\r\\n\\r\\n/* Bubble Buttons */\\r\\n.bubble-btn {\\r\\n  display: flex;\\r\\n  align-items: center;\\r\\n  justify-content: center;\\r\\n  padding: 0.8rem 1.8rem;\\r\\n  border-radius: 30px;\\r\\n  font-weight: 600;\\r\\n  font-size: 1.1rem;\\r\\n  cursor: pointer;\\r\\n  transition: all 0.3s ease;\\r\\n  position: relative;\\r\\n  overflow: hidden;\\r\\n  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);\\r\\n  z-index: 1;\\r\\n}\\r\\n\\r\\n.primary-bubble {\\r\\n  background: linear-gradient(135deg, var(--primary), var(--primary-dark));\\r\\n  color: white;\\r\\n  box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.3);\\r\\n}\\r\\n\\r\\n.secondary-bubble {\\r\\n  background: rgba(255, 255, 255, 0.8);\\r\\n  backdrop-filter: blur(10px);\\r\\n  -webkit-backdrop-filter: blur(10px);\\r\\n  color: var(--text);\\r\\n  border: 1px solid rgba(var(--primary-rgb), 0.3);\\r\\n}\\r\\n\\r\\n.bubble-btn::before {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  top: 0;\\r\\n  left: 0;\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n  background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 50%);\\r\\n  transform: translateX(-100%) rotate(45deg);\\r\\n  transition: transform 0.6s ease;\\r\\n  z-index: -1;\\r\\n}\\r\\n\\r\\n.bubble-btn:hover {\\r\\n  transform: translateY(-5px) scale(1.03);\\r\\n}\\r\\n\\r\\n.primary-bubble:hover {\\r\\n  box-shadow: 0 12px 30px rgba(var(--primary-rgb), 0.4);\\r\\n}\\r\\n\\r\\n.secondary-bubble:hover {\\r\\n  box-shadow: 0 12px 30px rgba(var(--primary-rgb), 0.2);\\r\\n  border-color: rgba(var(--primary-rgb), 0.5);\\r\\n}\\r\\n\\r\\n.bubble-btn:hover::before {\\r\\n  transform: translateX(100%) rotate(45deg);\\r\\n}\\r\\n\\r\\n.btn-icon {\\r\\n  margin-right: 0.8rem;\\r\\n  font-size: 1.2rem;\\r\\n  transition: transform 0.3s ease;\\r\\n}\\r\\n\\r\\n.bubble-btn:hover .btn-icon {\\r\\n  transform: scale(1.2);\\r\\n}\\r\\n\\r\\n.btn-text {\\r\\n  position: relative;\\r\\n}\\r\\n\\r\\n.btn-text::after {\\r\\n  content: '';\\r\\n  position: absolute;\\r\\n  bottom: -2px;\\r\\n  left: 0;\\r\\n  width: 0;\\r\\n  height: 2px;\\r\\n  background: currentColor;\\r\\n  transition: width 0.3s ease;\\r\\n}\\r\\n\\r\\n.bubble-btn:hover .btn-text::after {\\r\\n  width: 100%;\\r\\n}\\r\\n\\r\\n/* Form elements */\\r\\ninput, \\r\\ntextarea, \\r\\nselect {\\r\\n  width: 100%;\\r\\n  padding: 0.75rem;\\r\\n  border: 1px solid var(--border);\\r\\n  border-radius: 4px;\\r\\n  font-family: inherit;\\r\\n  font-size: 1rem;\\r\\n  transition: border-color 0.2s ease;\\r\\n}\\r\\n\\r\\ninput:focus, \\r\\ntextarea:focus, \\r\\nselect:focus {\\r\\n  outline: none;\\r\\n  border-color: var(--primary);\\r\\n}\\r\\n\\r\\nlabel {\\r\\n  display: block;\\r\\n  margin-bottom: 0.5rem;\\r\\n  font-weight: 500;\\r\\n}\\r\\n\\r\\n/* Grid system */\\r\\n.grid {\\r\\n  display: grid;\\r\\n  grid-template-columns: repeat(12, 1fr);\\r\\n  gap: 1.5rem;\\r\\n}\\r\\n\\r\\n/* Utility classes */\\r\\n.text-center {\\r\\n  text-align: center;\\r\\n}\\r\\n\\r\\n.mb-1 {\\r\\n  margin-bottom: 0.5rem;\\r\\n}\\r\\n\\r\\n.mb-2 {\\r\\n  margin-bottom: 1rem;\\r\\n}\\r\\n\\r\\n.mb-3 {\\r\\n  margin-bottom: 1.5rem;\\r\\n}\\r\\n\\r\\n.mb-4 {\\r\\n  margin-bottom: 2rem;\\r\\n}\\r\\n\\r\\n.mb-5 {\\r\\n  margin-bottom: 3rem;\\r\\n}\\r\\n\\r\\n.mt-1 {\\r\\n  margin-top: 0.5rem;\\r\\n}\\r\\n\\r\\n.mt-2 {\\r\\n  margin-top: 1rem;\\r\\n}\\r\\n\\r\\n.mt-3 {\\r\\n  margin-top: 1.5rem;\\r\\n}\\r\\n\\r\\n.mt-4 {\\r\\n  margin-top: 2rem;\\r\\n}\\r\\n\\r\\n.mt-5 {\\r\\n  margin-top: 3rem;\\r\\n}\\r\\n\\r\\n.py-1 {\\r\\n  padding-top: 0.5rem;\\r\\n  padding-bottom: 0.5rem;\\r\\n}\\r\\n\\r\\n.py-2 {\\r\\n  padding-top: 1rem;\\r\\n  padding-bottom: 1rem;\\r\\n}\\r\\n\\r\\n.py-3 {\\r\\n  padding-top: 1.5rem;\\r\\n  padding-bottom: 1.5rem;\\r\\n}\\r\\n\\r\\n.py-4 {\\r\\n  padding-top: 2rem;\\r\\n  padding-bottom: 2rem;\\r\\n}\\r\\n\\r\\n.py-5 {\\r\\n  padding-top: 3rem;\\r\\n  padding-bottom: 3rem;\\r\\n}\\r\\n\\r\\n.px-1 {\\r\\n  padding-left: 0.5rem;\\r\\n  padding-right: 0.5rem;\\r\\n}\\r\\n\\r\\n.px-2 {\\r\\n  padding-left: 1rem;\\r\\n  padding-right: 1rem;\\r\\n}\\r\\n\\r\\n.px-3 {\\r\\n  padding-left: 1.5rem;\\r\\n  padding-right: 1.5rem;\\r\\n}\\r\\n\\r\\n.px-4 {\\r\\n  padding-left: 2rem;\\r\\n  padding-right: 2rem;\\r\\n}\\r\\n\\r\\n.px-5 {\\r\\n  padding-left: 3rem;\\r\\n  padding-right: 3rem;\\r\\n}\\r\\n\\r\\n/* Responsive utilities */\\r\\n@media (max-width: 768px) {\\r\\n  .hide-mobile {\\r\\n    display: none;\\r\\n  }\\r\\n}\\r\\n\\r\\n@media (min-width: 769px) {\\r\\n  .hide-desktop {\\r\\n    display: none;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Map styles */\\r\\n.map-container {\\r\\n  width: 100%;\\r\\n  height: 500px;\\r\\n  border-radius: 8px;\\r\\n  overflow: hidden;\\r\\n}\\r\\n\\r\\n/* Leaflet specific styles */\\r\\n.leaflet-container {\\r\\n  width: 100%;\\r\\n  height: 100%;\\r\\n}\\r\\n\\r\\n.leaflet-popup-content-wrapper {\\r\\n  border-radius: 8px;\\r\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\r\\n}\\r\\n\\r\\n.leaflet-popup-content {\\r\\n  margin: 12px 16px;\\r\\n  line-height: 1.5;\\r\\n}\\r\\n\\r\\n/* Animation utilities */\\r\\n.fade-in {\\r\\n  animation: fadeIn 0.5s ease-in;\\r\\n}\\r\\n\\r\\n@keyframes fadeIn {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n  }\\r\\n}\\r\\n\\r\\n.slide-in {\\r\\n  animation: slideIn 0.5s ease-out;\\r\\n}\\r\\n\\r\\n@keyframes slideIn {\\r\\n  from {\\r\\n    transform: translateY(20px);\\r\\n    opacity: 0;\\r\\n  }\\r\\n  to {\\r\\n    transform: translateY(0);\\r\\n    opacity: 1;\\r\\n  }\\r\\n}\\r\\n\\r\\n.section {\\r\\n  padding: 4rem 0;\\r\\n}\\r\\n\\r\\n.section-title {\\r\\n  font-size: 2.5rem;\\r\\n  margin-bottom: 2rem;\\r\\n  text-align: center;\\r\\n  color: var(--text);\\r\\n}\\r\\n\\r\\n.section-subtitle {\\r\\n  font-size: 1.2rem;\\r\\n  margin-bottom: 3rem;\\r\\n  text-align: center;\\r\\n  color: var(--text-light);\\r\\n  max-width: 800px;\\r\\n  margin-left: auto;\\r\\n  margin-right: auto;\\r\\n}\\r\\n\\r\\n@media (max-width: 768px) {\\r\\n  .section {\\r\\n    padding: 3rem 0;\\r\\n  }\\r\\n  \\r\\n  .section-title {\\r\\n    font-size: 2rem;\\r\\n  }\\r\\n}\\r\\n\\r\\n/* Card styles */\\r\\n.card {\\r\\n  background-color: var(--background);\\r\\n  border-radius: 8px;\\r\\n  box-shadow: 0 4px 12px var(--shadow);\\r\\n  overflow: hidden;\\r\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\r\\n}\\r\\n\\r\\n.card:hover {\\r\\n  transform: translateY(-5px);\\r\\n  box-shadow: 0 8px 24px var(--shadow);\\r\\n}\\r\\n\\r\\n/* Form styles */\\r\\n.form-group {\\r\\n  margin-bottom: 1.5rem;\\r\\n}\\r\\n\\r\\n.form-label {\\r\\n  display: block;\\r\\n  margin-bottom: 0.5rem;\\r\\n  font-weight: 500;\\r\\n  color: var(--text);\\r\\n}\\r\\n\\r\\n.form-input {\\r\\n  width: 100%;\\r\\n  padding: 0.75rem;\\r\\n  border: 1px solid var(--border);\\r\\n  border-radius: 4px;\\r\\n  font-size: 1rem;\\r\\n  background-color: var(--background);\\r\\n  color: var(--text);\\r\\n  transition: border-color 0.2s ease;\\r\\n}\\r\\n\\r\\n.form-input:focus {\\r\\n  outline: none;\\r\\n  border-color: var(--primary);\\r\\n}\\r\\n\\r\\n/* Animation keyframes */\\r\\n@keyframes slideUp {\\r\\n  from {\\r\\n    opacity: 0;\\r\\n    transform: translateY(20px);\\r\\n  }\\r\\n  to {\\r\\n    opacity: 1;\\r\\n    transform: translateY(0);\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes pulse {\\r\\n  0% {\\r\\n    transform: scale(1);\\r\\n    opacity: 0.6;\\r\\n  }\\r\\n  50% {\\r\\n    transform: scale(1.1);\\r\\n    opacity: 0.3;\\r\\n  }\\r\\n  100% {\\r\\n    transform: scale(1);\\r\\n    opacity: 0.6;\\r\\n  }\\r\\n}\\r\\n\\r\\n@keyframes float {\\r\\n  0% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n  50% {\\r\\n    transform: translateY(-10px);\\r\\n  }\\r\\n  100% {\\r\\n    transform: translateY(0px);\\r\\n  }\\r\\n} \\r\\n\\r\\n        .gps-button-div{\\r\\n            position: absolute;\\r\\n            right: 20px;\\r\\n            bottom: 35px;\\r\\n            z-index: 1000;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n        }\\r\\n\\r\\n        .gps-button{\\r\\n            height: 50px;\\r\\n            width: 50px;\\r\\n            border-radius: 50%;\\r\\n            background-color: var(--background);\\r\\n            border-color: var(--border);\\r\\n            border-style: solid;\\r\\n            color: var(--text);\\r\\n            cursor: pointer;\\r\\n            display: flex;\\r\\n            justify-content: center;\\r\\n            align-items: center;\\r\\n            box-shadow: 0 2px 6px var(--shadow);\\r\\n            transition: transform 0.2s ease, box-shadow 0.2s ease;\\r\\n        }\\r\\n\\r\\n        .gps-button:hover {\\r\\n          transform: scale(1.1);\\r\\n          box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);\\r\\n        }\\r\\n\\r\\n        .gps-icon{\\r\\n            width: 80%;\\r\\n        }\\r\\n\\r\\n@tailwind base;\\r\\n@tailwind components;\\r\\n@tailwind utilities;\\r\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./styles/globals.css\n"));

/***/ })

});