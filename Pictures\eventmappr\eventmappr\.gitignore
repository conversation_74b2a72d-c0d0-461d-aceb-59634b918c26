# Dependencies
/node_modules
/.pnp
.pnp.js
/Backend/node_modules
/Backend/.env

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem
temp.md

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local env files
.env*.local

# Vercel
.vercel

# Typescript
*.tsbuildinfo
next-env.d.ts

# Production build files
/dist
.parcel-cache

# Environment files
.env
.env.development.local
.env.test.local
.env.production.local
config.js

# HTTPS certificates
/certs
*.pem

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo

# Misc
.cache/
.netlify
