<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>EventMappr – Community Events</title>

  <!-- Stylesheets -->
  <link rel="stylesheet" href="styles.css" />
  <link rel="stylesheet" href="src/style/Home.css" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"/>
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet" />

  <!-- Scripts -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollToPlugin.min.js"></script>
  <script src="https://cdn.emailjs.com/sdk/2.3.2/email.min.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
  <script src="auth.js"></script>

</head>
<body>
  <header>
    <div class="header-title">
      <h1>🗺️ EventMappr</h1>
      <p class="tagline">Discover & Share Local Events</p>
      <button id="themeToggleBtn" title="Toggle Dark Mode" 
        style="position: fixed; top: 3rem; right: 1rem; background: none; border: none; font-size: 1.5rem; cursor: pointer; z-index: 1000;">
        🌙
      </button>
    </div>
  </header>

  <nav class="navbar navbar-expand-lg navbar-light bg-light fixed-top">
    <div class="container">
      <a class="navbar-brand" href="#">🗺️ EventMappr</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto d-flex flex-row flex-wrap">
          <li class="nav-item mx-2"><a class="nav-link" href="#hero">Home</a></li>
          <li class="nav-item mx-2"><a class="nav-link" href="#features">Features</a></li>
          <li class="nav-item mx-2"><a class="nav-link" href="#how-it-works">How It Works</a></li>
          <li class="nav-item mx-2"><a class="nav-link" href="explore.html#map-section">Map</a></li>
          <li class="nav-item mx-2"><a class="nav-link" href="#faq-section">FAQs</a></li>
          <li class="nav-item mx-2" id="authLinks">
            <a class="nav-link" href="auth.html">Sign In</a>
          </li>
          <li class="nav-item mx-2 d-none" id="userProfile">
            <div class="d-flex align-items-center">
              <span class="nav-link me-2" id="userName"></span>
              <button class="btn btn-link nav-link p-0" id="logoutBtn">Logout</button>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <section id="hero" class="hero-section flex-center">
    <div class="hero-card">
      <div class="hero-content">
        <div class="hero-left">
          <h1 class="hero-title">A new way to discover & share events</h1>
          <p class="hero-subtitle">EventMappr helps you find, pin, and share local happenings with your community. Start exploring or add your own event now!</p>
          <div class="hero-btn-group">
            <a href="explore.html" class="btn btn-primary hero-btn">Start Exploring</a>
            <a href="#features" class="btn btn-outline hero-btn">Learn More</a>
          </div>
          <div class="hero-stats">
            <div class="hero-stat"><span class="stat-value" data-target="2500">0</span><span class="stat-label">Events</span></div>
            <div class="hero-stat"><span class="stat-value" data-target="1200">0</span><span class="stat-label">Users</span></div>
            <div class="hero-stat"><span class="stat-value" data-target="50">0</span><span class="stat-label">Cities</span></div>
          </div>
        </div>
        <div class="hero-right">
          <div class="hero-img-grid">
            <div class="hero-img-box"><img src="./assets/heropage.jpg" alt="Event Crowd"></div>
            <span class="hero-float-icon icon-star">★</span>
            <span class="hero-float-icon icon-pin">📍</span>
            <span class="hero-float-icon icon-share">🔗</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="features" class="features-section">
    <div class="container">
      <h2 class="section-title text-center">Features</h2>
      <div class="row">
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">📌</div>
            <h3>Pin Events</h3>
            <p>Add your events to the map for everyone to see</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">🔍</div>
            <h3>Explore</h3>
            <p>Discover exciting events happening around you</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="feature-card">
            <div class="feature-icon">📢</div>
            <h3>Share</h3>
            <p>Share the events with your community</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <section id="how-it-works" class="how-it-works-section">
    <div class="container">
      <h2 class="section-title text-center">How It Works</h2>
      <div class="row">
        <div class="col-md-4">
          <div class="step-card">
            <div class="step-number">1</div>
            <h3>Add Your Event</h3>
            <p>Fill in the event details and location</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="step-card">
            <div class="step-number">2</div>
            <h3>Pin on Map</h3>
            <p>Your event appears on the interactive map</p>
          </div>
        </div>
        <div class="col-md-4">
          <div class="step-card">
            <div class="step-number">3</div>
            <h3>Share & Connect</h3>
            <p>Let others discover your event</p>
          </div>
        </div>
      </div>
    </div>
  </section>

 <!-- create-contact-us -->
  <div class="controls">
    <form id="eventForm">
      <input class="input" type="text" id="eventName" placeholder="Event name" required />
      <select class="input" id="eventType" required>
        <option value="">Type</option>
        <option value="Music">Music</option>
        <option value="Volunteering">Volunteering</option>
        <option value="Technology">Technology</option>
        <option value="Market">Market</option>
        <option value="Art">Art</option>
        <option value="input">Other</option> <!--updated part-->
      </select>
      <button type="submit" class="btna btn-primary d-flex align-items-center gap-2">
        Add Event <span>➕</span>
      </button>

    </form>
    </select>
    <button id="locateBtn" class="btna btn-danger d-flex align-items-center gap-2">
      Find Nearby <span>📍</span>
    </button>
  </div>
  </div>

  <div id="map"></div>

  <footer>
    <p>Built with ❤️ using <a href="https://leafletjs.com/" target="_blank">Leaflet.js</a></p>
create-contact-us
    <p>&copy; 2025 EventMappr | <a href="https://github.com/Bhavya1352/eventmappr" target="_blank">GitHub</a> | <a href="#privacy-policy">Privacy Policy</a> | <a href="contact.html">Contact Us</a></p>

    <p>&copy; 2025 EventMappr | <a href="https://github.com/Bhavya1352/eventmappr" target="_blank">GitHub</a> | <a
        href="#privacy-policy">Privacy Policy</a> | <a href="#contact">Contact Us</a></p>
 main
  </footer>
 <!-- Floating Feedback Button -->
 <!-- main -->
  <button id="feedbackBtn" title="Send Feedback">💬</button>
  <div id="feedbackModal">
    <div class="modal-content">
      <span class="close-btn">&times;</span>
      <h2 style="color: #667eea; margin-bottom: 20px;">📝 Send Feedback</h2>
      <form id="feedbackForm">
        <label for="feedbackName">Name: <span style="color: #e74c3c;">*</span></label>
        <input type="text" id="feedbackName" name="name" required />

        <label for="feedbackEmail">Email: <span style="color: #e74c3c;">*</span></label>
        <input type="email" id="feedbackEmail" name="email" required />

        <label for="feedbackType">Type of Feedback: <span style="color: #e74c3c;">*</span></label>
        <select id="feedbackType" name="type" required>
          <option value="">Select feedback type</option>
          <option value="Bug Report">🐛 Bug Report</option>
          <option value="Feature Request">💡 Feature Request</option>
          <option value="General Feedback">💬 General Feedback</option>
          <option value="UI/UX Improvement">🎨 UI/UX Improvement</option>
          <option value="Performance Issue">⚡ Performance Issue</option>
          <option value="Compliment">👏 Compliment</option>
          <option value="Other">📝 Other</option>
        </select>

        <label for="feedbackText">Your Feedback: <span style="color: #e74c3c;">*</span></label>
        <textarea id="feedbackText" name="message" rows="5" required></textarea>

        <input type="hidden" name="time" id="feedbackTime" />
        <button type="submit" class="submit-btn">Submit Feedback</button>
      </form>
    </div>
  </div>

  <!-- Scroll to top button -->
  
  <div class="content">
    <div style="height: 2500px;"></div>
  </div>

  <div class="scroll-to-top-container">
    <div class="animated-border"></div>
    <button id="scrollToTopBtn" title="Go to top">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M12 4L6 10M12 4L18 10M12 4V20" stroke="#000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      </svg>
    </button>
  </div>

<div class="faqsdiv">
   <link rel="stylesheet" href="faq.css" />
   <section class="faq-section" id="faq-section">
    <h2 class="section-title text-center">Frequently Asked Questions</h2>
    
    <div class="faq-item">
      <div class="faq-question">What is EventMappr?</div>
      <div class="faq-answer">
        EventMappr is a lightweight, open-source web app that lets you discover, add, and explore local community events pinned on an interactive map.
      </div>
    </div>
    <div class="faq-item">
      <div class="faq-question">Is EventMappr free to use?</div>
      <div class="faq-answer">
        It's completely free and open-source under the MIT license.
      </div>
    </div>
    <div class="faq-item">
      <div class="faq-question">How do I add a new event?</div>
      <div class="faq-answer">
        Click anywhere on the map to drop a pin, then fill in the event details like title, category, date/time, and organizer.
      </div>
    </div>
    <div class="faq-item">
      <div class="faq-question">Does EventMappr store my events permanently?</div>
      <div class="faq-answer">
        EventMappr uses localStorage, so events stay saved in your browser unless you clear your data.
      </div>
    </div> 
    <div class="faq-item">
      <div class="faq-question">Can I filter events by category?</div>
      <div class="faq-answer">
        You can filter by Music, Tech, Volunteering, Market, and Art.
      </div>
    </div>
    <div class="faq-item">
      <div class="faq-question">Is there a mobile version?</div>
      <div class="faq-answer">
        Yes, the UI is fully responsive and optimized for mobile and desktop.
      </div>
    </div>
     </div>
   </section>
</div>
  <footer>
    <div class="footer-content">
      <div class="footer-logo">
        <i class="fas fa-map-marker-alt"></i>
        <span>EventMappr</span>
      </div>
      <div class="footer-links">
        <a href="#">Privacy Policy</a>
        <a href="#">Terms of Use</a>
        <a href="#">Contact</a>
      </div>
      <div class="social-links">
        <a href="#"><i class="fab fa-github"></i></a>
        <a href="#"><i class="fab fa-twitter"></i></a>
        <a href="#"><i class="fab fa-instagram"></i></a>
      </div>
    </div>
    <div class="copyright">
      <p>&copy; 2025 EventMappr. All rights reserved.</p>
    </div>
    <p>Built with ❤️ using <a href="https://leafletjs.com/" target="_blank">Leaflet.js</a></p>
    <p>&copy; 2025 EventMappr | <a href="https://github.com/Bhavya1352/eventmappr" target="_blank">GitHub</a> | <a href="#privacy-policy">Privacy Policy</a> | <a href="#contact">Contact Us</a></p>
  </footer>
  <script src="https://cdn.botpress.cloud/webchat/v3.0/inject.js" defer></script>
<script src="https://files.bpcontent.cloud/2025/06/17/10/20250617102015-6A94C26W.js" defer></script>
    

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script src="script.js"></script>
  <script src="scroll-to-top.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);

      gsap.from('.navbar', { opacity: 0, y: -40, duration: 1, ease: 'power3.out' });
      gsap.from('.hero-title', { opacity: 0, y: 40, duration: 1, ease: 'power3.out' });
      gsap.from('.hero-subtitle', { opacity: 0, y: 40, duration: 1, delay: 0.2, ease: 'power3.out' });
      gsap.from('.hero-btn-group', { opacity: 0, y: 30, duration: 0.8, delay: 0.5, ease: 'power3.out' });
      gsap.from('.hero-stats .hero-stat', {
        opacity: 0,
        y: 30,
        duration: 0.7,
        stagger: 0.15,
        delay: 0.7,
        ease: 'power3.out'
      });
      gsap.from('.hero-img-box', { opacity: 0, scale: 0.85, duration: 1, delay: 0.7, ease: 'power3.out' });

      gsap.utils.toArray('.hero-float-icon').forEach((icon, i) => {
        gsap.to(icon, {
          y: 18,
          repeat: -1,
          yoyo: true,
          duration: 2 + i,
          ease: 'sine.inOut',
          delay: i * 0.3
        });
      });

      gsap.utils.toArray('.feature-card').forEach((card, i) => {
        gsap.from(card, {
          scrollTrigger: {
            trigger: card,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          },
          opacity: 0,
          y: 40,
          duration: 0.8,
          delay: i * 0.15,
          ease: 'power3.out'
        });
      });

      gsap.utils.toArray('.step-card').forEach((card, i) => {
        gsap.from(card, {
          scrollTrigger: {
            trigger: card,
            start: 'top 80%',
            toggleActions: 'play none none reverse'
          },
          opacity: 0,
          y: 40,
          duration: 0.8,
          delay: i * 0.15,
          ease: 'power3.out'
        });
      });

      const statCounters = document.querySelectorAll('.stat-value');
      statCounters.forEach(counter => {
        const target = +counter.getAttribute('data-target');
        const duration = 1.5;

        ScrollTrigger.create({
          trigger: counter,
          start: 'top 90%',
          once: true,
          onEnter: () => {
            gsap.fromTo(counter,
              { innerText: 0 },
              {
                innerText: target,
                duration: duration,
                ease: 'power1.out',
                snap: { innerText: 1 },
                onUpdate: function () {
                  counter.innerText = Math.floor(counter.innerText).toLocaleString();
                },
                onComplete: function () {
                  counter.innerText = target.toLocaleString() + '+';
                }
              });
          }
        });
      });
    });
    document.querySelectorAll('.faq-question').forEach(question => {
      question.addEventListener('click', () => {
        const item = question.parentElement;
        document.querySelectorAll('.faq-item').forEach(i => {
          if (i !== item) i.classList.remove('active');
        });
        item.classList.toggle('active');
      });
    });
  </script>

  <!-- Firebase SDK -->
  <script>
    // Initialize Firebase
    firebase.initializeApp({
      apiKey: "AIzaSyDOhPO50_bSDoNJ1lKOV7NRN672dq_1Ldg",
      authDomain: "eventmappr23.firebaseapp.com",
      projectId: "eventmappr23",
      storageBucket: "eventmappr23.firebasestorage.app",
      messagingSenderId: "767568582880",
      appId: "1:767568582880:web:c5438cf3a3d371a47d1885",
      measurementId: "G-VG4E8PJ29S"
    });

    // Auth state observer
    firebase.auth().onAuthStateChanged((user) => {
      const authLinks = document.getElementById('authLinks');
      const userProfile = document.getElementById('userProfile');
      const userName = document.getElementById('userName');
      const logoutBtn = document.getElementById('logoutBtn');

      if (user) {
        // User is signed in
        if (authLinks) authLinks.classList.add('d-none');
        if (userProfile) {
          userProfile.classList.remove('d-none');
          if (userName) userName.textContent = user.displayName || user.email;
        }
      } else {
        // User is signed out
        if (authLinks) authLinks.classList.remove('d-none');
        if (userProfile) userProfile.classList.add('d-none');
      }
    });

    // Logout handler
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', () => {
        firebase.auth().signOut();
      });
    }
  </script>
</body>
</html>
