/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --border-hover: #cbd5e1;
    --accent-primary: #3b82f6;
    --accent-secondary: #1e40af;
    --accent-success: #10b981;
    --accent-warning: #f59e0b;
    --accent-danger: #ef4444;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

[data-theme="dark"] {
    /* Dark theme colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #334155;
    --border-hover: #475569;
    --accent-primary: #60a5fa;
    --accent-secondary: #3b82f6;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Navigation */
.navbar {
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(10px);
    background-color: var(--bg-card);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.back-home-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: var(--accent-primary);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    font-size: 0.875rem;
}

.back-home-btn:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-md);
}

.logo-text h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.125rem;
}

.logo-text p {
    color: var(--text-muted);
    font-size: 0.875rem;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.icon-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    width: 2.5rem;
    height: 2.5rem;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.icon-btn:hover {
    background-color: var(--bg-tertiary);
    border-color: var(--border-hover);
    color: var(--text-primary);
}

.icon-btn .moon-icon {
    display: none;
}

[data-theme="dark"] .icon-btn .sun-icon {
    display: none;
}

[data-theme="dark"] .icon-btn .moon-icon {
    display: block;
}

.online-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-muted);
}

.online-dot {
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--accent-success);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Search Bar */
.search-bar {
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    transition: var(--transition);
}

.search-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    position: relative;
}

.search-container .search-icon {
    position: absolute;
    left: 2.25rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

#search-input {
    width: 100%;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 0.75rem 1rem 0.75rem 3rem;
    font-size: 1rem;
    color: var(--text-primary);
    transition: var(--transition);
}

#search-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.search-clear {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.search-clear:hover {
    color: var(--text-primary);
    background-color: var(--bg-secondary);
}

/* Main Content */
.main {
    flex: 1;
    padding: 2rem 0;
}

/* Stats Dashboard */
.stats-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition);
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.messages-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.users-icon {
    background: linear-gradient(135deg, #10b981, #047857);
}

.activity-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.stat-content h3 {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-content p {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Post Form Card */
.post-form-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
}

.form-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.form-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.form-controls {
    display: flex;
    gap: 0.5rem;
}

.secondary-btn {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.secondary-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.danger-btn {
    background-color: var(--accent-danger);
    color: white;
    border: 1px solid var(--accent-danger);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.danger-btn:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}

.message-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.form-group input,
.form-group textarea,
.form-group select {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

.textarea-container {
    position: relative;
}

.textarea-container textarea {
    width: 100%;
    min-height: 120px;
    resize: vertical;
    padding-bottom: 2.5rem;
}

.textarea-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: absolute;
    bottom: 0.75rem;
    left: 1rem;
    right: 1rem;
}

.formatting-tips {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.char-count {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.input-feedback {
    font-size: 0.75rem;
    min-height: 1rem;
    transition: var(--transition);
}

.input-feedback.error {
    color: var(--accent-danger);
}

.input-feedback.success {
    color: var(--accent-success);
}

.form-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.submit-btn {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
}

.submit-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    background: var(--bg-tertiary);
    color: var(--text-muted);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Messages Section */
.messages-section {
    margin-bottom: 4rem;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.section-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.sort-select,
.filter-select {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: var(--transition);
}

.sort-select:hover,
.filter-select:hover {
    border-color: var(--border-hover);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.empty-icon {
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

.empty-state h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: var(--text-muted);
    font-size: 1rem;
}

/* Messages Container */
.messages-container {
    display: flex;
    flex-direction: column;
    gap: 1.25rem;
}

/* Message Card */
.message-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: var(--transition);
    box-shadow: var(--shadow-sm);
    position: relative;
}

.message-card:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.message-card.highlighted {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 1px var(--accent-primary);
}

.message-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.message-avatar {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 1.125rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.message-info {
    flex: 1;
    min-width: 0;
}

.message-info h3 {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.message-category {
    font-size: 0.75rem;
    padding: 0.125rem 0.5rem;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    color: var(--text-muted);
}

.message-info p {
    font-size: 0.875rem;
    color: var(--text-muted);
}

.message-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: var(--transition);
}

.message-card:hover .message-actions {
    opacity: 1;
}

.message-action-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-muted);
    width: 2rem;
    height: 2rem;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.message-action-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.message-action-btn.danger:hover {
    background-color: var(--accent-danger);
    border-color: var(--accent-danger);
    color: white;
}

.message-content {
    color: var(--text-primary);
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: 1rem;
}

/* Load More */
.load-more-container {
    text-align: center;
    margin-top: 2rem;
}

.load-more-btn {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.load-more-btn:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* Footer */
.footer {
    background-color: var(--bg-card);
    border-top: 1px solid var(--border-color);
    padding: 2rem 0;
    margin-top: auto;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-left {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.footer-logo .logo-icon {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    width: 1.5rem;
    height: 1.5rem;
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
}

.footer p {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 100%;
    position: relative;
    z-index: 1001;
    animation: slideIn 0.3s ease-out;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.5rem 0;
}

.modal-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.modal-close:hover {
    color: var(--text-primary);
    background-color: var(--bg-tertiary);
}

.modal-body {
    padding: 1rem 1.5rem;
}

.modal-body p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 0 1.5rem 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
    
    .nav-container {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .nav-left {
        justify-content: space-between;
    }
    
    .nav-right {
        justify-content: center;
    }
    
    .logo-text h1 {
        font-size: 1.25rem;
    }
    
    .main {
        padding: 1.5rem 0;
    }
    
    .stats-dashboard {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .stat-card {
        padding: 1rem;
    }
    
    .post-form-card {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .section-controls {
        justify-content: center;
    }
    
    .message-card {
        padding: 1rem;
    }
    
    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .message-avatar {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .modal-content {
        margin: 1rem;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .back-home-btn {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }
    
    .logo {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }
    
    .logo-icon {
        width: 2.5rem;
        height: 2.5rem;
    }
    
    .logo-text h1 {
        font-size: 1.125rem;
    }
    
    .post-form-card {
        padding: 1rem;
    }
    
    .form-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .section-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .sort-select,
    .filter-select {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .navbar,
    .search-bar,
    .post-form-card,
    .footer,
    .message-actions {
        display: none !important;
    }
    
    .main {
        padding: 0;
    }
    
    .message-card {
        box-shadow: none;
        border: 1px solid #ccc;
        page-break-inside: avoid;
        margin-bottom: 1rem;
    }
}