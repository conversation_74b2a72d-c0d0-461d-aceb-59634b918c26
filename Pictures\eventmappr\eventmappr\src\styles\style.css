/* ---------- GLOBAL STYLES ---------- */
:root {
    --primary-color: #731DBE;
    --primary-hover: #5a17a0;
    --primary-light: #f3eaff;

    /* Light theme variables */
    --bg-color: #ffffff;
    --text-color: #2d3748;
    --text-muted: #718096;
    --nav-bg: #f8f9fa;
    --card-bg: #ffffff;
    --border-color: rgba(0, 0, 0, 0.1);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --footer-bg: #f8f9fa;
    --popup-bg: #ffffff;
    --hover-bg: #f7fafc;
    --btn-primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --btn-primary-hover: linear-gradient(135deg, #5a6fd6 0%, #6a4492 100%);
    --section-bg: #ffffff;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Dark theme variables - Updated with darker, more professional colors */
[data-theme="dark"] {
    --bg-color: #0a0a0f;
    --text-color: #e2e8f0;
    --text-muted: #94a3b8;
    --nav-bg: #13131a;
    --card-bg: #1a1a23;
    --border-color: rgba(99, 102, 241, 0.1);
    --shadow-color: rgba(0, 0, 0, 0.5);
    --input-bg: #13131a;
    --input-border: #2d2d3a;
    --footer-bg: #13131a;
    --popup-bg: #1a1a23;
    --hover-bg: #22222c;
    --btn-primary-bg: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    --btn-primary-hover: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
    --section-bg: #0a0a0f;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body {
    margin: 0;
    font-family: 'Poppins', 'Inter', sans-serif;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    height: 100vh;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Delete button hover effect */
.custom-popup button[onclick*="deleteEvent"]:hover {
    background: #e84393 !important;
    transform: translateY(-2px);
}

/* Toast animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateX(-50%) translateY(10px); }
    to { opacity: 1; transform: translateX(-50%) translateY(0); }
}
/* Hover effect for the trash button */
.custom-popup button[onclick*="deleteEvent"]:hover {
    background: #e84393 !important;
    transform: scale(1.05);
}

/* Hover effect for the View Details button */
.custom-popup a[href*="event-details"]:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4) !important;
}

h1,
h2,
h3,
h4,
h5,
h6,
.stat-value,
.section-title,
.feature-card h3,
.step-card h3 {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-weight: 700;
}

.hero-subtitle,
.stat-label,
.btn,
.btn-outline,
.feature-card p,
.step-card p {
    font-family: 'Inter', 'Poppins', sans-serif;
}

/* Centered Header Styling */
header {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #4b0082 100%);
    color: white;
    padding: 1.5rem 2rem;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    margin-top: 20px;
}

.header-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    gap: 0.5rem;
    position: relative;
    width: 100%;
    max-width: 600px;
}

.header-title h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-align: center;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.header-title h1:hover {
    transform: scale(1.02);
    filter: brightness(1.1);
}

.tagline {
    font-family: 'Inter', sans-serif;
    font-size: 1.1rem;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.95);
    margin: 0;
    text-align: center;
    opacity: 0.9;
    transition: all 0.3s ease;
    line-height: 1.4;
}

.tagline:hover {
    opacity: 1;
    transform: translateY(-1px);
}

#themeToggleBtn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

[data-theme="dark"] #themeToggleBtn {
    background: rgba(99, 102, 241, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.1);
}

#themeToggleBtn:hover {
    transform: scale(1.1) rotate(8deg);
    border-color: rgba(99, 102, 241, 0.4);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.2);
}

[data-theme="dark"] #themeToggleBtn:hover {
    background: rgba(99, 102, 241, 0.15);
    border-color: rgba(99, 102, 241, 0.4);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.2);
}

#themeToggleBtn:active {
    transform: scale(0.95);
}

@media (max-width: 768px) {
    #themeToggleBtn {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
        top: 0.75rem;
        right: 0.75rem;
    }
}

@media (max-width: 480px) {
    #themeToggleBtn {
        width: 35px;
        height: 35px;
        font-size: 1rem;
        top: 0.5rem;
        right: 0.5rem;
    }
}

/* Dark mode support */
.dark-mode header {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-mode #themeToggleBtn {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-mode #themeToggleBtn:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Animation for header elements */
@keyframes headerFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header-title h1,
.tagline,
#themeToggleBtn {
    animation: headerFadeIn 0.6s ease-out;
}

.header-title h1 {
    animation-delay: 0.1s;
}

.tagline {
    animation-delay: 0.2s;
}

#themeToggleBtn {
    animation-delay: 0.3s;
}

/* HERO SECTION - Improved & Consistent */

.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4vw 0 2vw 0;
    position: relative;
    z-index: 1;
}

.hero-section::before {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: url('data:image/svg+xml,%3Csvg width="100%25" height="100%25" xmlns="http://www.w3.org/2000/svg"%3E%3Cellipse cx="70%25" cy="-10%25" rx="60%25" ry="40%25" fill="%234b0082" fill-opacity="0.07"/%3E%3Cellipse cx="-10%25" cy="80%25" rx="40%25" ry="30%25" fill="%238a2be2" fill-opacity="0.07"/%3E%3C/svg%3E');
    z-index: 0;
}

.hero-card {
    background: #fff;
    border-radius: 2.2rem;
    box-shadow: 0 8px 32px rgba(44, 62, 80, 0.10), 0 1.5px 6px rgba(44, 62, 80, 0.06);
    padding: 3rem 3.5rem;
    max-width: 1100px;
    width: 95vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    z-index: 2;
}

.hero-content {
    display: flex;
    gap: 3rem;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.hero-left {
    flex: 1 1 340px;
    min-width: 300px;
    max-width: 480px;
}

.hero-title {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-size: 2.7rem;
    font-weight: 700;
    color: #232946;
    margin-bottom: 1.1rem;
    line-height: 1.15;
    letter-spacing: -1px;
}

.hero-subtitle {
    font-size: 1.15rem;
    color: #5f6c7b;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-btn-group {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
    justify-content: flex-start;
}

.hero-btn {
    padding: 1.2rem 2.5rem;
    border-radius: 2.5rem;
    font-size: 1.25rem;
    font-family: 'Inter', 'Poppins', sans-serif;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(44, 62, 80, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 180px;
    justify-content: center;
    letter-spacing: 0.5px;
}

.hero-btn:hover,
.hero-btn:focus {
    box-shadow: 0 8px 35px rgba(138, 43, 226, 0.25), 0 2px 10px rgba(44, 62, 80, 0.15);
    background: linear-gradient(135deg, #4b0082 0%, #8a2be2 100%);
    color: #fff;
    transform: translateY(-3px) scale(1.05);
    text-decoration: none;
}

.hero-btn:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

.btn-outline {
    background: rgba(255, 255, 255, 0.95);
    color: #4b0082;
    border: 3px solid #4b0082;
    backdrop-filter: blur(10px);
}

.btn-outline:hover {
    background: linear-gradient(135deg, #f3eaff 0%, #e8f0ff 100%);
    color: #4b0082;
    border-color: #8a2be2;
    transform: translateY(-3px) scale(1.05);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    position: relative;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.hero-stats {
    display: flex;
    gap: 2.5rem;
    margin-top: 1.5rem;
    border-top: 1px solid #ececec;
    padding-top: 1.2rem;
}

.hero-stat {
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    min-width: 80px;
}

.hero-stat:not(:last-child) {
    border-right: 1px solid #ececec;
    padding-right: 2rem;
}

.hero-stat:hover {
    box-shadow: none;
    border-radius: 0;
    border: none;
    transform: none;
    background: none;
}

.stat-value {
    font-family: 'Poppins', 'Inter', sans-serif;
    font-size: 1.35rem;
    font-weight: 700;
    color: #4b0082;
}

.stat-label {
    font-size: 0.98rem;
    color: #5f6c7b;
    margin-top: 0.1rem;
}

.hero-right {
    flex: 1 1 320px;
    min-width: 260px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-img-grid {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    gap: 0;
    position: relative;
    width: 400px;
    height: 400px;
    margin: 0 auto;
}

.hero-img-box {
    border-radius: 1.3rem;
    overflow: hidden;
    box-shadow: 0 4px 16px rgba(44, 62, 80, 0.10);
    background: #f3eaff;
    width: 100%;
    height: 100%;
    transition: box-shadow 0.3s, border 0.3s, transform 0.3s;
}

.hero-img-box img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    aspect-ratio: 1/1;
    transition: transform 0.3s;
}

.hero-img-box:hover {
    box-shadow: 0 8px 32px rgba(138, 43, 226, 0.18), 0 1.5px 6px rgba(44, 62, 80, 0.10);
    border: 3px solid #8a2be2;
    transform: scale(1.04) rotate(-2deg);
}

.hero-float-icon {
    position: absolute;
    font-size: 2rem;
    opacity: 2;
    pointer-events: none;
}

.icon-star {
    color: #f7c948;
    top: -10px;
    left: 60px;
}

.icon-pin {
    color: #4b0082;
    bottom: 20px;
    right: 200px;
}

.icon-share {
    color: #8a2be2;
    top: 100px;
    right: 10px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: none;
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: none;
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.85);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes floatIcon {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-18px) scale(1.1);
    }
}

@media (max-width: 1100px) {
    .hero-card {
        padding: 2rem 1.5rem;
    }
    .hero-img-grid {
        width: 400px;
        height: 400px;
    }
}

@media (max-width: 900px) {
    .hero-content {
        flex-direction: column;
        gap: 2.5rem;
    }
    .hero-left,
    .hero-right {
        max-width: 100%;
        text-align: center;
    }
    .hero-stats,
    .hero-btn-group {
        justify-content: center;
    }
}

@media (max-width: 600px) {
    .hero-card {
        padding: 1.2rem 0.2rem;
        border-radius: 1.2rem;
    }
    .hero-title {
        font-size: 1.5rem;
    }
    .hero-img-grid {
        width: 300px;
        height: 300px;
        gap: 0.5rem;
    }
    .hero-img-box {
        border-radius: 0.7rem;
    }
    .hero-btn {
        padding: 1rem 2rem;
        font-size: 1.1rem;
        min-width: 160px;
    }
}


/* Features Section */

.features-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

.section-title {
    color: #4b0082;
    margin-bottom: 3rem;
    font-size: 2.5rem;
    text-align: center;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.22s, border 0.22s, transform 0.22s, background 0.22s;
    cursor: pointer;
}

.feature-card:hover {
    box-shadow: 0 8px 32px rgba(76, 0, 229, 0.13);
    border-radius: 1.1rem;
    border: 2px solid #8a2be2;
    background: #f3eaff;
    transform: scale(1.04) translateY(-4px);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    transition: transform 0.22s;
}

.feature-card:hover .feature-icon {
    transform: rotate(-12deg) scale(1.18);
}


/* How It Works Section */

.how-it-works-section {
    padding: 5rem 0;
    background: white;
}

.step-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    position: relative;
    transition: box-shadow 0.22s, border 0.22s, transform 0.22s, background 0.22s;
    cursor: pointer;
}

.step-card:hover {
    box-shadow: 0 8px 32px rgba(76, 0, 229, 0.13);
    border-radius: 1.1rem;
    border: 2px solid #8a2be2;
    background: #f3eaff;
    transform: scale(1.04) translateY(-4px);
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4b0082, #8a2be2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-weight: bold;
    transition: transform 0.22s;
}

.step-card:hover .step-number {
    transform: rotate(-12deg) scale(1.18);
}


/* Map Section */

#map-section {
    padding: 5rem 0;
    background: #f8f9fa;
}

#map {
    height: 70vh;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: linear-gradient(90deg, #4b0082, #8a2be2);
    color: whitesmoke;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}


/* 🧾 Tagline just under logo */

.tagline {
    text-align: center;
    color: whitesmoke;
}

header h1 {
    margin: 0.2rem 0;
}

.controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    padding: 1.5rem;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(44, 62, 80, 0.08);
    margin-bottom: 2rem;
    flex-wrap: nowrap;
    overflow-x: auto;
    min-height: 70px;
}

#eventForm {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    min-width: 0;
    flex-wrap: nowrap;
}

.input {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.95rem;
    font-family: 'Inter', sans-serif;
    transition: all 0.3s ease;
    background: #f8fafc;
    min-width: 120px;
    flex-shrink: 0;
}

.input:focus {
    outline: none;
    border-color: #667eea;
    background: #fff;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input:hover {
    border-color: #cbd5e0;
    background: #fff;
}

/* Specific input sizing */
#eventName {
    min-width: 150px;
    flex: 1;
}

#eventDate,
#eventTime {
    min-width: 140px;
}

#eventType {
    min-width: 130px;
}

/* Button styling */
.controls button,
.controls .btn {
    padding: 0.75rem 1.25rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.95rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
    flex-shrink: 0;
    min-width: fit-content;
}

.controls button:hover,
.controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Button colors */
.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e53e3e, #c53030);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #38a169, #2f855a);
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: #4a5568;
    border: 2px solid #e2e8f0;
}

.btn-outline-secondary:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
    color: #2d3748;
}

/* Responsive design */
@media (max-width: 1200px) {
    .controls {
        flex-wrap: wrap;
        gap: 1rem;
    }
    
    #eventForm {
        flex-wrap: wrap;
        width: 100%;
    }
    
    .controls button,
    .controls .btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 768px) {
    .controls {
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;
    }
    
    #eventForm {
        flex-direction: column;
        width: 100%;
    }
    
    .input {
        width: 100%;
        min-width: unset;
    }
    
    .controls button,
    .controls .btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .controls {
        padding: 0.75rem;
        gap: 0.75rem;
    }
    
    .input {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }
    
    .controls button,
    .controls .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
}

footer {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #4b0082 100%);
    color: #fff;
    text-align: center;
    padding: 2.5rem 0 1.5rem 0;
    font-size: 1.1rem;
    font-family: 'Poppins', 'Inter', sans-serif;
    box-shadow: 0 -4px 24px rgba(102, 126, 234, 0.18);
    border-top: 3px solid rgba(255,255,255,0.12);
    margin-top: 3rem;
    position: relative;
    z-index: 10;
    transition: all 0.3s ease;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto 1.5rem auto;
    padding: 0 2rem;
    gap: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.7rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #fff;
    letter-spacing: -0.5px;
}

.footer-logo i {
    color: #FACC15;
    font-size: 2rem;
    filter: drop-shadow(0 0 8px rgba(250,204,21,0.3));
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #fff;
    font-weight: 500;
    font-size: 1.05rem;
    opacity: 0.92;
    transition: color 0.2s, opacity 0.2s;
    position: relative;
}

.footer-links a:hover {
    color: #FACC15;
    opacity: 1;
    text-decoration: none;
}

.social-links {
    display: flex;
    gap: 1.2rem;
}

.social-links a {
    color: #fff;
    font-size: 1.4rem;
    background: rgba(255,255,255,0.08);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s, color 0.2s, transform 0.2s;
}

.social-links a:hover {
    background: #FACC15;
    color: #4b0082;
    transform: translateY(-3px) scale(1.08);
}

.copyright {
    color: rgba(255,255,255,0.8);
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

footer p, footer a {
    font-size: 1rem;
    color: #fff;
    opacity: 0.92;
    margin: 0.2rem 0.5rem;
}

footer a:hover {
    color: #FACC15;
    opacity: 1;
    text-decoration: underline;
}

@media (max-width: 900px) {
    .footer-content {
        flex-direction: column;
        gap: 1.5rem;
        padding: 0 1rem;
        text-align: center;
    }
    .footer-links, .social-links {
        justify-content: center;
    }
}


/* ---------- LEAFLET CUSTOM POPUP ---------- */

.custom-popup .leaflet-popup-content-wrapper {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: none;
    padding: 0;
    overflow: hidden;
}

.custom-popup .leaflet-popup-content {
    margin: 0;
    padding: 0;
    min-width: 250px;
}

.custom-popup .leaflet-popup-tip {
    background: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.custom-popup .leaflet-popup-close-button {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #333;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
}

.custom-popup .leaflet-popup-close-button:hover {
    background: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}


/* ---------- BUTTON STYLES ---------- */

.btn {
    transition: all 0.3s ease;
    border-radius: 25px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.leaflet-control-zoom {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.leaflet-control-zoom a {
    border-radius: 0 !important;
    transition: all 0.3s ease;
}

.leaflet-control-zoom a:hover {
    background-color: #f8f9fa !important;
    color: #667eea !important;
}

.marker-cluster {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    color: white;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.footer-links>a{
  color: white;
}
.social-links>a{
  color: white;
}
/* Custom popup styles */

.custom-popup .leaflet-popup-content-wrapper {
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  border: none;
  padding: 0;
  overflow: hidden;
}

#feedbackBtn:hover {
    transform: scale(1.15) translateY(-2px);
    background: linear-gradient(135deg, #8a2be2, #4b0082);
    box-shadow: 0 8px 30px rgba(139, 43, 226, 0.6);
}

#feedbackBtn:active {
    transform: scale(1.05);
}


/* ---------- FEEDBACK MODAL ---------- */

#feedbackModal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, #ffffff, #f8f9ff);
    margin: 5% auto;
    padding: 30px;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(75, 0, 130, 0.15);
    border: 1px solid rgba(139, 43, 226, 0.1);
    box-sizing: border-box;
    position: relative;
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    z-index: 1001;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #8a2be2;
}


/* ---------- FEEDBACK FORM ---------- */

#feedbackForm {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

#feedbackForm label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #232946;
    text-align: left;
}

#feedbackForm input,
#feedbackForm select,
#feedbackForm textarea {
    width: 100%;
    box-sizing: border-box;
    padding: 12px;
    font-size: 1rem;
    border-radius: 10px;
    border: 2px solid #e9ecef;
    background: #fff;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

#feedbackForm input:focus,
#feedbackForm select:focus,
#feedbackForm textarea:focus {
    outline: none;
    border-color: #8a2be2;
    box-shadow: 0 0 0 3px rgba(139, 43, 226, 0.1);
}

#feedbackForm .submit-btn {
    background: linear-gradient(135deg, #4b0082, #8a2be2);
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
    cursor: pointer;
    align-self: center;
    width: 100%;
    max-width: 200px;
    box-shadow: 0 4px 15px rgba(139, 43, 226, 0.3);
}

#feedbackForm .submit-btn:hover {
    background: linear-gradient(135deg, #8a2be2, #4b0082);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 43, 226, 0.4);
}


/* ---------- DESKTOP FORM LAYOUT ---------- */

@media (min-width: 900px) {
    #feedbackForm {
        display: grid;
        grid-template-columns: 140px 1fr;
        gap: 20px 15px;
        align-items: center;
    }
    #feedbackForm label {
        text-align: right;
    }
    #feedbackForm .submit-btn {
        grid-column: 1 / -1;
        justify-self: center;
        width: fit-content;
        max-width: none;
    }
  
  .custom-popup .leaflet-popup-content {
    min-width: 200px;
  }
}


/* ---------- YES/NO HOVER ---------- */

#yes-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(241, 79, 253, 0.6), 0 0 12px rgba(97, 0, 166, 0.7);
}

#no-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(76, 0, 229, 0.5), 0 0 12px rgba(37, 2, 48, 0.6);
}

#suggestBtn {
    background: linear-gradient(135deg, #00b894, #00cec9);
    color: white;
    font-weight: 600;
    border: none;
    box-shadow: 0 4px 10px rgba(0, 206, 201, 0.3);
}

#suggestBtn:hover {
    transform: scale(1.05);
    background: linear-gradient(135deg, #00cec9, #00b894);
    box-shadow: 0 6px 20px rgba(0, 206, 201, 0.5);
}


/* Animations */

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}


/* ---------- MOBILE RESPONSIVENESS ---------- */

@media (max-width: 768px) {
    .hero-section {
        padding: 6rem 0;
    }
    .hero-title {
        font-size: 3rem;
    }
    .hero-tagline {
        font-size: 1.5rem;
    }
    .explore-btn {
        padding: 1.2rem 2.5rem;
        font-size: 1.2rem;
    }
    .feature-card,
    .step-card {
        margin-bottom: 2rem;
    }
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    form {
        flex-direction: column;
    }
    .custom-popup .leaflet-popup-content {
        min-width: 200px;
    }
    #feedbackBtn {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    .modal-content {
        margin: 10% auto;
        padding: 20px;
        width: 95%;
    }
}


html {
  scroll-behavior: smooth;
}

/* Smooth transition for theme toggle */
body,
header,
.controls,
input,
select,
button,
footer,
#map {
  transition: background-color 0.4s ease, color 0.4s ease, border 0.4s ease;
}



/* ---------- EVENT FORM - REFINED ---------- */


form .form-control,
form .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    background-color: #ffffff;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}



form .form-control:focus,
form .form-select:focus {
    border-color: #8a2be2;
    box-shadow: 0 0 0 0.2rem rgba(138, 43, 226, 0.2);
}



.controls {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 1rem;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    margin-bottom: 1rem;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(44, 62, 80, 0.06);
}


/* Remove old .input if using Bootstrap */

.input {
    /* Optional: if used elsewhere, keep minimal styling */
    border-radius: 8px;
    padding: 0.5rem;
    border: 1px solid #dee2e6;
    background-color: #ffffff;
    color: #4b0082;
}

.controls form {
    flex: 1 1 auto;
    width: 100%;
}



@media (max-width: 768px) {
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
}


.page-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.page-wrapper main {
    flex: 1;
}
#googleLogin{
    color: #731DBE;
}

/*Scroll to top Button */

.content {
  padding: 2rem;
  text-align: center;
}

.scroll-to-top-container {
  position: fixed;
  bottom: 100px;
  right: 30px;
  width: 60px;
  height: 60px;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.scroll-to-top-container.show {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

#scrollToTopBtn {
  position: absolute;
  width: 60px;
  height: 60px;
  background-color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  color: black;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  transition: box-shadow 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

#scrollToTopBtn:hover {
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.3); 
}

/* Animated rotating border */
.animated-border {
  position: absolute;
  width: 70px;
  height: 70px;
  border: 3px solid transparent;
  border-top: 3px solid #a11fc1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  top: -5px;
  left: -5px;
  z-index: 1;
  transition: animation-duration 0.2s ease;
}

/* Speed up spin on hover */
.scroll-to-top-container:hover .animated-border {
  animation-duration: 0.4s;
}

@keyframes spin {
  0%   { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced Dark Mode Overrides */
[data-theme="dark"] {
    color-scheme: dark;
}

[data-theme="dark"] body {
    background: linear-gradient(to bottom, #0a0a0f, #13131a);
}

[data-theme="dark"] .navbar {
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    background: rgba(19, 19, 26, 0.95);
}

[data-theme="dark"] .navbar-light .navbar-nav .nav-link {
    color: var(--text-color);
}

[data-theme="dark"] .navbar-light .navbar-nav .nav-link:hover {
    color: #6366f1;
}

[data-theme="dark"] .navbar .nav-link,
[data-theme="dark"] .navbar-light .nav-link {
    color: var(--text-color) !important;
}

[data-theme="dark"] .feature-card,
[data-theme="dark"] .step-card,
[data-theme="dark"] .auth-section,
[data-theme="dark"] .modal-content,
[data-theme="dark"] .custom-popup .leaflet-popup-content-wrapper {
    background: rgba(26, 26, 35, 0.95);
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
}

[data-theme="dark"] .feature-card:hover,
[data-theme="dark"] .step-card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(129, 140, 248, 0.2);
}

[data-theme="dark"] .input,
[data-theme="dark"] select,
[data-theme="dark"] textarea {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

[data-theme="dark"] .input:focus,
[data-theme="dark"] select:focus,
[data-theme="dark"] textarea:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
    background-color: rgba(19, 19, 26, 0.95);
}

[data-theme="dark"] .footer {
    background: linear-gradient(to bottom, rgba(15, 15, 20, 0.98), rgba(10, 10, 15, 0.99));
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .footer-content {
    background: rgba(20, 20, 25, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

[data-theme="dark"] .footer-logo i {
    color: #6366f1;
    font-size: 1.5rem;
    text-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
}

[data-theme="dark"] .footer-logo span {
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
}

[data-theme="dark"] .footer-links {
    display: flex;
    gap: 2rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

[data-theme="dark"] .footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.95rem;
    position: relative;
}

[data-theme="dark"] .footer-links a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #4f46e5, #7c3aed);
    transition: width 0.3s ease;
}

[data-theme="dark"] .footer-links a:hover {
    color: #ffffff;
}

[data-theme="dark"] .footer-links a:hover::after {
    width: 100%;
}

[data-theme="dark"] .social-links {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

[data-theme="dark"] .social-links a {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.25rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .social-links a:hover {
    color: #ffffff;
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .copyright {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 1rem 0;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] footer p {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin: 0.5rem 0;
}

[data-theme="dark"] footer a {
    color: #a5b4fc;
    text-decoration: none;
    transition: all 0.3s ease;
}

[data-theme="dark"] footer a:hover {
    color: #c7d2fe;
    text-decoration: none;
}

/* Responsive Footer Dark Theme */
@media (max-width: 768px) {
    [data-theme="dark"] .footer-content {
        padding: 1.5rem;
    }

    [data-theme="dark"] .footer-links {
        flex-wrap: wrap;
        gap: 1rem;
    }

    [data-theme="dark"] .social-links {
        gap: 1rem;
    }

    [data-theme="dark"] .social-links a {
        width: 35px;
        height: 35px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    [data-theme="dark"] .footer-content {
        padding: 1rem;
    }

    [data-theme="dark"] .footer-logo span {
        font-size: 1.1rem;
    }

    [data-theme="dark"] .footer-links {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    [data-theme="dark"] .social-links a {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
}

/* Dark Theme Specific Styles */
[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 50%, var(--bg-tertiary) 100%);
}

[data-theme="dark"] .feature-card,
[data-theme="dark"] .step-card {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
}

[data-theme="dark"] .feature-icon,
[data-theme="dark"] .step-number {
    color: var(--accent-primary);
    background: rgba(129, 140, 248, 0.1);
    border: 2px solid rgba(129, 140, 248, 0.2);
}

[data-theme="dark"] .custom-popup {
    background: var(--card-bg) !important;
    border: 1px solid var(--card-border) !important;
    box-shadow: var(--card-shadow) !important;
}

[data-theme="dark"] .custom-popup .leaflet-popup-content-wrapper {
    background: transparent;
    color: var(--text-primary);
}

[data-theme="dark"] .custom-popup .leaflet-popup-tip {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
}

[data-theme="dark"] .leaflet-container {
    background: var(--bg-primary) !important;
}

[data-theme="dark"] #themeToggleBtn {
    background: rgba(129, 140, 248, 0.1);
    border: 2px solid rgba(129, 140, 248, 0.2);
    color: var(--accent-primary);
    padding: 8px;
    border-radius: 50%;
}

[data-theme="dark"] #themeToggleBtn:hover {
    background: rgba(129, 140, 248, 0.15);
    border-color: rgba(129, 140, 248, 0.3);
}

[data-theme="dark"] .auth-section {
    background: linear-gradient(145deg, rgba(26, 26, 35, 0.9), rgba(19, 19, 26, 0.9));
    border: 1px solid var(--card-border);
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 0 0 1px rgba(129, 140, 248, 0.1);
}

[data-theme="dark"] .auth-message {
    color: var(--text-primary);
}

[data-theme="dark"] .controls {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    box-shadow: var(--card-shadow);
}

[data-theme="dark"] .input {
    background: var(--input-bg);
    border: 1px solid var(--input-border);
    color: var(--input-text);
}

[data-theme="dark"] .input:focus {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
}

[data-theme="dark"] .btn {
    border: none;
}

[data-theme="dark"] .btn-primary {
    background: var(--btn-primary-bg);
}

[data-theme="dark"] .btn-primary:hover {
    background: var(--btn-primary-hover);
}

[data-theme="dark"] .btn-outline-secondary {
    border: 1px solid var(--accent-primary);
    color: var(--accent-primary);
}

[data-theme="dark"] .btn-outline-secondary:hover {
    background: var(--accent-primary);
    color: white;
}

/* Dark Theme Animations */
@keyframes darkGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

[data-theme="dark"] .auth-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary), var(--accent-primary));
    background-size: 200% 100%;
    animation: darkGradient 3s linear infinite;
}

/* Feature Cards Dark Theme Enhancement */
[data-theme="dark"] .feature-card {
    background: rgba(26, 26, 35, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    transition: box-shadow 0.3s ease;
}

[data-theme="dark"] .feature-card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(129, 140, 248, 0.2);
}

[data-theme="dark"] .feature-card h3 {
    color: #ffffff;
    font-weight: 600;
    margin: 1rem 0;
    font-size: 1.25rem;
}

[data-theme="dark"] .feature-card p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

[data-theme="dark"] .feature-icon {
    color: #818cf8;
    background: rgba(129, 140, 248, 0.1);
    border: 2px solid rgba(129, 140, 248, 0.2);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.75rem;
    margin: 0 auto 1rem;
    transition: all 0.3s ease;
}

[data-theme="dark"] .feature-card:hover .feature-icon {
    background: rgba(129, 140, 248, 0.15);
    border-color: rgba(129, 140, 248, 0.3);
    box-shadow: 0 0 20px rgba(129, 140, 248, 0.3);
}

/* Section Title Dark Theme Enhancement */
[data-theme="dark"] .section-title {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .section-title::after {
    background: linear-gradient(90deg, #818cf8, #a78bfa);
    opacity: 0.8;
}

/* Features Section Dark Theme */
[data-theme="dark"] .features-section {
    background: linear-gradient(to bottom, 
        rgba(10, 10, 15, 0.95), 
        rgba(13, 13, 18, 0.98)
    );
    padding: 4rem 0;
    position: relative;
}

[data-theme="dark"] .features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, 
        transparent,
        rgba(129, 140, 248, 0.2),
        transparent
    );
}

/* How It Works Dark Theme Enhancement */
[data-theme="dark"] .how-it-works-section {
    background: linear-gradient(to bottom,
        rgba(44, 21, 88, 0.95),
        rgba(30, 15, 60, 0.98)
    );
    padding: 4rem 0;
    position: relative;
    border-top: 1px solid rgba(147, 51, 234, 0.1);
    border-bottom: 1px solid rgba(147, 51, 234, 0.1);
}

[data-theme="dark"] .how-it-works-section .section-title {
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .how-it-works-section .section-title::after {
    background: linear-gradient(90deg, #9333ea, #c084fc);
    opacity: 0.8;
}

[data-theme="dark"] .step-card {
    background: rgba(58, 30, 114, 0.4);
    border: 1px solid rgba(147, 51, 234, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 16px;
    transition: box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

[data-theme="dark"] .step-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #9333ea, #c084fc);
    opacity: 0;
    transition: opacity 0.3s ease;
}

[data-theme="dark"] .step-card:hover {
    border-color: rgba(147, 51, 234, 0.4);
    box-shadow: 0 12px 40px rgba(147, 51, 234, 0.2);
}

[data-theme="dark"] .step-card:hover::before {
    opacity: 1;
}

[data-theme="dark"] .step-number {
    background: rgba(147, 51, 234, 0.15);
    color: #c084fc;
    border: 2px solid rgba(147, 51, 234, 0.3);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

[data-theme="dark"] .step-card:hover .step-number {
    background: rgba(147, 51, 234, 0.25);
    border-color: rgba(147, 51, 234, 0.5);
    box-shadow: 0 0 20px rgba(147, 51, 234, 0.3);
}

[data-theme="dark"] .step-card h3 {
    color: #fff;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

[data-theme="dark"] .step-card p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    line-height: 1.6;
}

/* Add purple glow effect */
[data-theme="dark"] .how-it-works-section::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: radial-gradient(
        circle at center,
        rgba(147, 51, 234, 0.1) 0%,
        transparent 70%
    );
    pointer-events: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    [data-theme="dark"] .step-card {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }

    [data-theme="dark"] .step-number {
        width: 35px;
        height: 35px;
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    [data-theme="dark"] .step-card h3 {
        font-size: 1.1rem;
    }
}

/* Hero Card Dark Theme Enhancement */
[data-theme="dark"] .hero-card {
    background: linear-gradient(135deg, 
        rgba(20, 20, 30, 0.95) 0%, 
        rgba(15, 15, 25, 0.98) 50%, 
        rgba(10, 10, 20, 0.99) 100%
    );
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.4),
        inset 0 0 0 1px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

[data-theme="dark"] .hero-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #818cf8, #a78bfa, #c084fc);
    border-radius: 20px 20px 0 0;
}

[data-theme="dark"] .hero-title {
    color: #ffffff;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    line-height: 1.2;
}

[data-theme="dark"] .hero-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-weight: 400;
}

[data-theme="dark"] .hero-btn {
    background: linear-gradient(135deg, #818cf8 0%, #a78bfa 100%);
    color: #ffffff;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: 0 8px 25px rgba(129, 140, 248, 0.3);
    line-height: 1.5;
    min-height: 44px;
}

[data-theme="dark"] .hero-btn:hover {
    background: linear-gradient(135deg, #6b7af1 0%, #9b7ef3 100%);
    box-shadow: 0 12px 35px rgba(129, 140, 248, 0.4);
    color: #ffffff;
    text-decoration: none;
}

[data-theme="dark"] .hero-btn.btn-outline {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    box-shadow: none;
    padding: 0.5rem 1.5rem;
    border-radius: 15px;
    font-size: 1rem;
    min-height: 44px;
}

[data-theme="dark"] .hero-btn.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .hero-btn-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 2rem;
}

/* Responsive consistency */
@media (max-width: 768px) {
    [data-theme="dark"] .hero-btn,
    [data-theme="dark"] .hero-btn.btn-outline {
        padding: 0.625rem 1.25rem;
        font-size: 0.95rem;
        min-height: 40px;
    }
}

@media (max-width: 480px) {
    [data-theme="dark"] .hero-btn,
    [data-theme="dark"] .hero-btn.btn-outline {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        min-height: 38px;
    }
    
    [data-theme="dark"] .hero-btn-group {
        flex-direction: column;
        gap: 0.75rem;
    }
}

[data-theme="dark"] .navbar-brand {
    color: #ffffff !important;
    text-decoration: none;
    transition: opacity 0.3s ease;
}

[data-theme="dark"] .navbar-brand:hover {
    opacity: 0.9;
    color: #ffffff !important;
}

/* Also update the navbar-light class for dark theme */
[data-theme="dark"] .navbar-light .navbar-brand {
    color: #ffffff !important;
}

[data-theme="dark"] .navbar-light .navbar-brand:hover {
    color: #ffffff !important;
    opacity: 0.9;
}
