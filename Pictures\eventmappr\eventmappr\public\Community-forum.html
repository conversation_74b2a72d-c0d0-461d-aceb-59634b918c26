<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Community Forum - Connect & Share</title>
    <link rel="stylesheet" href="Community-forum.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <button id="back-home-btn" class="back-home-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                        <polyline points="9,22 9,12 15,12 15,22"/>
                    </svg>
                    Home
                </button>
                <div class="logo">
                    <div class="logo-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <div class="logo-text">
                        <h1>Community Forum</h1>
                        <p>Share thoughts, connect with others</p>
                    </div>
                </div>
            </div>
            <div class="nav-right">
                <button id="search-toggle" class="icon-btn" title="Search messages">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"/>
                        <path d="21 21l-4.35-4.35"/>
                    </svg>
                </button>
                <button id="theme-toggle" class="icon-btn" title="Toggle theme">
                    <svg class="sun-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"/>
                        <line x1="12" y1="1" x2="12" y2="3"/>
                        <line x1="12" y1="21" x2="12" y2="23"/>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                        <line x1="1" y1="12" x2="3" y2="12"/>
                        <line x1="21" y1="12" x2="23" y2="12"/>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
                    </svg>
                    <svg class="moon-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"/>
                    </svg>
                </button>
                <div class="online-indicator">
                    <div class="online-dot"></div>
                    <span id="online-count">1</span> online
                </div>
            </div>
        </div>
    </nav>

    <!-- Search Bar -->
    <div id="search-bar" class="search-bar hidden">
        <div class="search-container">
            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="11" cy="11" r="8"/>
                <path d="21 21l-4.35-4.35"/>
            </svg>
            <input type="text" id="search-input" placeholder="Search messages..." />
            <button id="search-clear" class="search-clear">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
            </button>
        </div>
    </div>

    <main class="main">
        <div class="container">
            <!-- Stats Dashboard -->
            <div class="stats-dashboard">
                <div class="stat-card">
                    <div class="stat-icon messages-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 id="total-messages">0</h3>
                        <p>Total Messages</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon users-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 id="unique-users">0</h3>
                        <p>Contributors</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon activity-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M13 2L3 14h9l-1 8 10-12h-9l1-8z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 id="last-activity">Never</h3>
                        <p>Last Activity</p>
                    </div>
                </div>
            </div>

            <!-- Post Form -->
            <div class="post-form-card">
                <div class="form-header">
                    <h2>Share Your Thoughts</h2>
                    <div class="form-controls">
                        <button id="clear-form" class="secondary-btn">Clear</button>
                    </div>
                </div>
                
                <form id="message-form" class="message-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Your Name *</label>
                            <input type="text" id="name" placeholder="Enter your name" maxlength="50" required>
                            <div class="input-feedback" id="name-feedback"></div>
                        </div>
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select id="category">
                                <option value="general">💬 General</option>
                                <option value="question">❓ Question</option>
                                <option value="announcement">📢 Announcement</option>
                                <option value="feedback">💡 Feedback</option>
                                <option value="discussion">🗣️ Discussion</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="message">Your Message *</label>
                        <div class="textarea-container">
                            <textarea id="message" placeholder="What's on your mind? Share your thoughts with the community..." maxlength="500" required></textarea>
                            <div class="textarea-footer">
                                <div class="formatting-tips">
                                    <span class="tip">💡 Pro tip: Use line breaks for better readability</span>
                                </div>
                                <div class="char-count">
                                    <span id="char-count">0</span>/500
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" id="submit-btn" class="submit-btn">
                            <svg class="btn-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="22" y1="2" x2="11" y2="13"/>
                                <polygon points="22,2 15,22 11,13 2,9 22,2"/>
                            </svg>
                            <span id="btn-text">Post Message</span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Messages Section -->
            <div class="messages-section">
                <div class="section-header">
                    <h2 id="messages-title">Recent Messages</h2>
                    <div class="section-controls">
                        <select id="sort-select" class="sort-select">
                            <option value="newest">📅 Newest First</option>
                            <option value="oldest">📅 Oldest First</option>
                            <option value="name">👤 By Name</option>
                        </select>
                        <select id="filter-select" class="filter-select">
                            <option value="all">All Categories</option>
                            <option value="general">💬 General</option>
                            <option value="question">❓ Question</option>
                            <option value="announcement">📢 Announcement</option>
                            <option value="feedback">💡 Feedback</option>
                            <option value="discussion">🗣️ Discussion</option>
                        </select>
                        <button id="export-btn" class="secondary-btn" title="Export messages">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Empty State -->
                <div id="empty-state" class="empty-state">
                    <div class="empty-icon">
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
                        </svg>
                    </div>
                    <h3>No Messages Yet</h3>
                    <p>Be the first to start a conversation in our community!</p>
                </div>

                <!-- Messages Container -->
                <div id="messages-container" class="messages-container"></div>

                <!-- Load More Button -->
                <div id="load-more-container" class="load-more-container hidden">
                    <button id="load-more-btn" class="load-more-btn">
                        Load More Messages
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="6,9 12,15 18,9"/>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-left">
                    <div class="footer-logo">
                        <div class="logo-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        Community Forum
                    </div>
                    <p>© 2025 Community Forum. Built with ❤️ for connecting people.</p>
                </div>
                <div class="footer-right">
                    <button id="clear-all-btn" class="danger-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="3,6 5,6 21,6"/>
                            <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6"/>
                        </svg>
                        Clear All Messages
                    </button>
                </div>
            </div>
        </div>
    </footer>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="modal hidden">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Confirm Action</h3>
                <button id="modal-close" class="modal-close">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"/>
                        <line x1="6" y1="6" x2="18" y2="18"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <p id="modal-message">Are you sure you want to perform this action?</p>
            </div>
            <div class="modal-footer">
                <button id="modal-cancel" class="secondary-btn">Cancel</button>
                <button id="modal-confirm" class="danger-btn">Confirm</button>
            </div>
        </div>
    </div>

    <script src="Community-forum.js"></script>
</body>
</html>