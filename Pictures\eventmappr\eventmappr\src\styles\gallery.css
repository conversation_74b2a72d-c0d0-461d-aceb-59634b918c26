* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #2D3748 0%, #1A202C 100%);
    color: #E2E8F0;
    min-height: 100vh;
    line-height: 1.6;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: rgba(45, 55, 72, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(246, 224, 94, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #F6E05E;
}

.logo svg {
    color: #F6E05E;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    color: #CBD5E0;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #F6E05E;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 0;
    right: 0;
    height: 2px;
    background: #F6E05E;
    border-radius: 1px;
}

/* Main Content */
.main {
    flex: 1;
    padding: 2rem 0;
}

/* Hero Section */
.hero {
    text-align: center;
    margin-bottom: 3rem;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: #F7FAFC;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #F6E05E 0%, #ECC94B 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #CBD5E0;
    max-width: 600px;
    margin: 0 auto 2rem;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #F6E05E 0%, #ECC94B 100%);
    color: #2D3748;
    border: none;
    border-radius: 2rem;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(246, 224, 94, 0.3);
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(246, 224, 94, 0.4);
}

/* Upload Modal */
.upload-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.upload-modal.hidden {
    display: none;
}

.upload-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
}

.upload-content {
    position: relative;
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    background: rgba(45, 55, 72, 0.95);
    border-radius: 1rem;
    overflow: hidden;
    border: 1px solid rgba(246, 224, 94, 0.2);
    backdrop-filter: blur(20px);
    overflow-y: auto;
}

.upload-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(203, 213, 224, 0.1);
}

.upload-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #F7FAFC;
}

.upload-close {
    background: none;
    border: none;
    color: #CBD5E0;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.upload-close:hover {
    background: rgba(246, 224, 94, 0.1);
    color: #F6E05E;
}

.upload-form {
    padding: 1.5rem;
}

.upload-zone {
    position: relative;
    border: 2px dashed rgba(203, 213, 224, 0.3);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-zone:hover,
.upload-zone.dragover {
    border-color: #F6E05E;
    background: rgba(246, 224, 94, 0.05);
}

.upload-placeholder svg {
    color: #718096;
    margin-bottom: 1rem;
}

.upload-placeholder p {
    color: #CBD5E0;
    margin-bottom: 0.5rem;
}

.upload-link {
    color: #F6E05E;
    font-weight: 500;
}

.upload-placeholder small {
    color: #A0AEC0;
    font-size: 0.875rem;
}

.upload-preview {
    position: relative;
    display: inline-block;
}

.upload-preview img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 0.5rem;
    object-fit: cover;
}

.remove-image {
    position: absolute;
    top: -0.5rem;
    right: -0.5rem;
    background: #E53E3E;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-image:hover {
    background: #C53030;
    transform: scale(1.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: #E2E8F0;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    background: rgba(203, 213, 224, 0.1);
    border: 1px solid rgba(203, 213, 224, 0.2);
    border-radius: 0.5rem;
    color: #E2E8F0;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #F6E05E;
    background: rgba(246, 224, 94, 0.1);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #A0AEC0;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.btn-secondary,
.btn-primary {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-secondary {
    background: rgba(203, 213, 224, 0.1);
    color: #CBD5E0;
    border: 1px solid rgba(203, 213, 224, 0.2);
}

.btn-secondary:hover {
    background: rgba(203, 213, 224, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #F6E05E 0%, #ECC94B 100%);
    color: #2D3748;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(246, 224, 94, 0.3);
}

/* Success Message */
.success-message {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1100;
    background: linear-gradient(135deg, #48BB78 0%, #38A169 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px rgba(72, 187, 120, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.success-message:not(.hidden) {
    transform: translateX(0);
}

.success-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Filters */
.filters {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(45, 55, 72, 0.5);
    border-radius: 1rem;
    backdrop-filter: blur(10px);
}

.filter-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    background: rgba(203, 213, 224, 0.1);
    border: 1px solid rgba(203, 213, 224, 0.2);
    border-radius: 2rem;
    color: #CBD5E0;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: #F6E05E;
    color: #2D3748;
    border-color: #F6E05E;
    transform: translateY(-1px);
}

.search-container {
    position: relative;
    min-width: 250px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    background: rgba(203, 213, 224, 0.1);
    border: 1px solid rgba(203, 213, 224, 0.2);
    border-radius: 2rem;
    color: #E2E8F0;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #F6E05E;
    background: rgba(246, 224, 94, 0.1);
}

.search-input::placeholder {
    color: #A0AEC0;
}

.search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #A0AEC0;
    pointer-events: none;
}

/* Gallery Section */
.gallery-section {
    min-height: 400px;
}

/* Loading State */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 0;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(246, 224, 94, 0.3);
    border-top: 3px solid #F6E05E;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Gallery Grid */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

.gallery-item {
    background: rgba(45, 55, 72, 0.8);
    border-radius: 1rem;
    overflow: hidden;
    transition: all 0.4s ease;
    cursor: pointer;
    border: 1px solid rgba(203, 213, 224, 0.1);
    backdrop-filter: blur(10px);
}

.gallery-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(246, 224, 94, 0.3);
}

.gallery-item-image {
    width: 100%;
    aspect-ratio: 4/3;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.gallery-item:hover .gallery-item-image {
    transform: scale(1.05);
}

.gallery-item-content {
    padding: 1.5rem;
}

.gallery-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.gallery-item-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #F7FAFC;
    margin-bottom: 0.25rem;
}

.gallery-item-category {
    background: linear-gradient(135deg, #F6E05E 0%, #ECC94B 100%);
    color: #2D3748;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.gallery-item-caption {
    color: #CBD5E0;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.gallery-item-author {
    color: #A0AEC0;
    font-size: 0.8rem;
    font-style: italic;
}

.user-contributed {
    border-color: rgba(72, 187, 120, 0.3);
}

.user-contributed:hover {
    border-color: rgba(72, 187, 120, 0.5);
}

.user-badge {
    background: linear-gradient(135deg, #48BB78 0%, #38A169 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-left: 0.5rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 0;
    color: #A0AEC0;
}

.empty-state.hidden {
    display: none;
}

.empty-icon {
    color: #718096;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: #CBD5E0;
    margin-bottom: 0.5rem;
}

/* Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.lightbox.hidden {
    display: none;
}

.lightbox-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: rgba(45, 55, 72, 0.95);
    border-radius: 1rem;
    overflow: hidden;
    border: 1px solid rgba(246, 224, 94, 0.2);
    backdrop-filter: blur(20px);
}

.lightbox-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
}

.lightbox-close:hover {
    background: rgba(246, 224, 94, 0.8);
    color: #2D3748;
}

.lightbox-image {
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
    display: block;
}

.lightbox-info {
    padding: 1.5rem;
}

.lightbox-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.lightbox-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #F7FAFC;
    margin-bottom: 0.5rem;
}

.lightbox-meta {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.lightbox-category {
    background: linear-gradient(135deg, #F6E05E 0%, #ECC94B 100%);
    color: #2D3748;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.lightbox-author {
    color: #A0AEC0;
    font-size: 0.9rem;
    font-style: italic;
}

.lightbox-caption {
    color: #CBD5E0;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: rgba(26, 32, 44, 0.8);
    border-top: 1px solid rgba(246, 224, 94, 0.1);
    padding: 2rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #F6E05E;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: #CBD5E0;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #F6E05E;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    color: #CBD5E0;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: #F6E05E;
}

.footer-bottom {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(203, 213, 224, 0.1);
}

.footer-bottom p {
    color: #A0AEC0;
    font-size: 0.9rem;
}

/* Hidden utility */
.hidden {
    display: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        gap: 1rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-controls {
        justify-content: center;
    }

    .search-container {
        min-width: auto;
    }

    .gallery-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .footer-links {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .lightbox-content {
        margin: 1rem;
    }

    .lightbox-image {
        max-height: 50vh;
    }

    .upload-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }

    .form-actions {
        flex-direction: column;
    }

    .success-message {
        top: 1rem;
        right: 1rem;
        left: 1rem;
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1025px) {
    .gallery-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1400px) {
    .gallery-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}