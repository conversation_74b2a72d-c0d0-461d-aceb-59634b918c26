<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EventMappr - Authentication</title>
    <link rel="stylesheet" href="auth.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light fixed-top"id="navbar">
        <div class="container">
           <a class="navbar-brand" href="index.html">🗺️ EventMappr</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto align-items-center">
                <li class="nav-item">
                    <a class="nav-link" href="index.html">Home</a>
                </li>
                <li class="nav-item ms-2">
                    <button id="darkModeToggle" class="btn btn-outline-secondary py-1 px-2">
                    <i class="fas fa-moon"></i>
                    </button>
                </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="auth-container">
            <div class="auth-tabs">
                <div class="auth-tab active" data-tab="login">Login</div>
                <div class="auth-tab" data-tab="signup">Sign Up</div>
            </div>

            <form id="loginForm" class="auth-form active">
                <div class="mb-3">
                    <label for="loginEmail" class="form-label">Email address</label>
                    <input type="email" class="form-control" id="loginEmail" required>
                </div>
                <div class="mb-3 position-relative">
                    <label for="loginPassword" class="form-label">Password</label>
                    <input type="password" class="form-control" id="loginPassword" required>
                    <button type="button" class="toggle-password" data-target="loginPassword">
                        <i class="far fa-eye"></i>
                    </button>
                </div>
                <button type="submit" class="btn w-100" id="loginBtn">Login</button>
                <div class="error-message" id="loginError"></div>
            </form>

            <form id="signupForm" class="auth-form">
                <div class="mb-3">
                    <label for="signupName" class="form-label">Full Name</label>
                    <input type="text" class="form-control" id="signupName" required>
                </div>
                <div class="mb-3">
                    <label for="signupEmail" class="form-label">Email address</label>
                    <input type="email" class="form-control" id="signupEmail" required>
                </div>
                <div class="mb-3 position-relative">
                    <label for="signupPassword" class="form-label">Password</label>
                    <input type="password" class="form-control" id="signupPassword" required>
                    <button type="button" class="toggle-password" data-target="signupPassword">
                        <i class="far fa-eye"></i>
                    </button>
                </div>
                <button type="submit" class="btn w-100" id="signupBtn">Sign Up</button>
                <div class="error-message" id="signupError"></div>
            </form>

            <div class="social-login">
                <p>Or continue with</p>
                <button id="googleLogin" class="btn">
                    <i class="fab fa-google"></i> Continue with Google
                </button>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    
    <!-- Your auth script -->
    <script src="auth.js"></script>
</body>
</html> 