{"name": "eventmappr", "version": "0.1.0", "private": true, "description": "*EventMappr* is a lightweight, open-source community event mapping web app. Users can discover, add, and explore local events pinned on an interactive map. Built with *React, Next.js, and **Leaflet.js*, it offers a simple and engaging way to visualize what's happening around you.", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "aos": "^2.3.4", "express": "^5.1.0", "firebase": "^11.9.1", "framer-motion": "^12.23.0", "leaflet": "^1.9.4", "lucide-react": "^0.525.0", "mongoose": "^8.17.0", "next": "^15.4.4", "next-themes": "^0.4.6", "nodemon": "^3.1.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^4.2.1", "react-router-dom": "^7.6.2", "recharts": "^3.1.0", "scrollreveal": "^4.0.9"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "autoprefixer": "^10.4.21", "eslint": "^8.41.0", "eslint-config-next": "^13.4.3", "postcss": "^8.5.6", "tailwindcss": "^4.1.11"}}