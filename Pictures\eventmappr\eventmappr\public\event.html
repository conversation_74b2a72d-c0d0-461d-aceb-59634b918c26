<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EventMappr – All Events</title>
    <link rel="stylesheet" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet" />
</head>

<!-- event.html body section only -->

<body>
    <div class="page-wrapper d-flex flex-column min-vh-100">
        <header class="header d-flex justify-content-between align-items-center px-4 py-2">
            <nav>
                <a href="index.html" class="btn btn-outline">Back</a>
            </nav>
            <div class="text-center flex-grow-1">
                <h1>🗺️ EventMappr</h1>
                <p class="tagline">Discover & Share Local Events</p>
            </div>
            <div style="width: 100px;"></div>
        </header>

        <main class="container my-4 flex-grow-1">
            <h2 class="section-title">📅 All Events</h2>
            <div class="d-flex justify-content-end mb-3">
                <label for="sortSelect" class="me-2">Sort by:</label>
                <select id="sortSelect" class="input">
          <option value="location">Location</option>
        </select>
            </div>
            <ul id="eventList" class="list-group"></ul>
        </main>

        <footer>
            <p>Built with ❤️ using <a href="https://leafletjs.com/" target="_blank">Leaflet.js</a></p>
            <p>&copy; 2025 EventMappr |
                <a href="https://github.com/Bhavya1352/eventmappr" target="_blank">GitHub</a> |
                <a href="#privacy-policy">Privacy Policy</a> |
                <a href="#contact">Contact Us</a>
            </p>
        </footer>
    </div>

    <script src="event.js"></script>
</body>