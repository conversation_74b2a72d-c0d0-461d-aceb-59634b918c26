<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photo Gallery - EventMappr</title>
    <link rel="stylesheet" href="gallery.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    <circle cx="12" cy="9" r="3" stroke="currentColor" stroke-width="2"/>
                </svg>
                <span>EventMappr</span>
            </div>
            <nav class="nav">
                <a href="Home.html" class="nav-link">Home</a>
                <a href="#" class="nav-link">About Us</a>
                <a href="#" class="nav-link active">Gallery</a>
                <a href="Weather.html" class="nav-link">Weather Planner</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Hero Section -->
            <section class="hero">
                <h1 class="hero-title">Event Photo Gallery</h1>
                <p class="hero-subtitle">Relive the moments from our amazing community events and share your own experiences</p>
                <button class="share-btn" id="shareBtn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                        <polyline points="17,8 12,3 7,8" stroke="currentColor" stroke-width="2"/>
                        <line x1="12" y1="3" x2="12" y2="15" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Share Your Experience
                </button>
            </section>

            <!-- Upload Modal -->
            <div class="upload-modal hidden" id="uploadModal">
                <div class="upload-backdrop" id="uploadBackdrop"></div>
                <div class="upload-content">
                    <div class="upload-header">
                        <h2>Share Your Event Experience</h2>
                        <button class="upload-close" id="uploadClose">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                                <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                            </svg>
                        </button>
                    </div>
                    
                    <form class="upload-form" id="uploadForm">
                        <div class="upload-zone" id="uploadZone">
                            <div class="upload-placeholder" id="uploadPlaceholder">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                                    <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                                    <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                <p>Drop your image here or <span class="upload-link">browse files</span></p>
                                <small>Supports JPG, PNG, GIF up to 5MB</small>
                            </div>
                            <div class="upload-preview hidden" id="uploadPreview">
                                <img id="previewImage" src="" alt="Preview">
                                <button type="button" class="remove-image" id="removeImage">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                        <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                                        <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                                    </svg>
                                </button>
                            </div>
                            <input type="file" id="imageInput" accept="image/*" hidden>
                        </div>

                        <div class="form-group">
                            <label for="eventTitle">Event Title *</label>
                            <input type="text" id="eventTitle" name="title" required placeholder="e.g., Summer Music Festival 2025">
                        </div>

                        <div class="form-group">
                            <label for="eventCategory">Category *</label>
                            <select id="eventCategory" name="category" required>
                                <option value="">Select a category</option>
                                <option value="Technology">Technology</option>
                                <option value="Music">Music</option>
                                <option value="Art">Art</option>
                                <option value="Food">Food</option>
                                <option value="Sports">Sports</option>
                                <option value="Community">Community</option>
                                <option value="Business">Business</option>
                                <option value="Education">Education</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="eventCaption">Caption *</label>
                            <textarea id="eventCaption" name="caption" required placeholder="Share your experience and what made this event special..." rows="3"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="userName">Your Name</label>
                            <input type="text" id="userName" name="userName" placeholder="How should we credit you? (optional)">
                        </div>

                        <div class="form-actions">
                            <button type="button" class="btn-secondary" id="cancelUpload">Cancel</button>
                            <button type="submit" class="btn-primary" id="submitUpload">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" stroke-width="2"/>
                                    <polyline points="17,8 12,3 7,8" stroke="currentColor" stroke-width="2"/>
                                    <line x1="12" y1="3" x2="12" y2="15" stroke="currentColor" stroke-width="2"/>
                                </svg>
                                Share Photo
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Filter Controls -->
            <section class="filters">
                <div class="filter-controls">
                    <button class="filter-btn active" data-category="all">All Events</button>
                    <button class="filter-btn" data-category="Technology">Technology</button>
                    <button class="filter-btn" data-category="Music">Music</button>
                    <button class="filter-btn" data-category="Art">Art</button>
                    <button class="filter-btn" data-category="Food">Food</button>
                    <button class="filter-btn" data-category="Sports">Sports</button>
                    <button class="filter-btn" data-category="Community">Community</button>
                </div>
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="Search events..." id="searchInput">
                    <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                        <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                    </svg>
                </div>
            </section>

            <!-- Success Message -->
            <div class="success-message hidden" id="successMessage">
                <div class="success-content">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" stroke="currentColor" stroke-width="2"/>
                        <polyline points="22,4 12,14.01 9,11.01" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>Your photo has been shared successfully!</span>
                </div>
            </div>

            <!-- Gallery Container -->
            <section class="gallery-section">
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>Loading gallery...</p>
                </div>
                
                <div class="gallery-grid" id="galleryGrid">
                    <!-- Gallery items will be inserted here by JavaScript -->
                </div>

                <div class="empty-state hidden" id="emptyState">
                    <svg width="80" height="80" viewBox="0 0 24 24" fill="none" class="empty-icon">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                        <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                        <polyline points="21,15 16,10 5,21" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <h3>No events found</h3>
                    <p>Try adjusting your search or filter criteria</p>
                </div>
            </section>
        </div>
    </main>

    <!-- Lightbox Modal -->
    <div class="lightbox hidden" id="lightbox">
        <div class="lightbox-backdrop" id="lightboxBackdrop"></div>
        <div class="lightbox-content">
            <button class="lightbox-close" id="lightboxClose">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
                    <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
                </svg>
            </button>
            <img class="lightbox-image" id="lightboxImage" src="" alt="">
            <div class="lightbox-info">
                <div class="lightbox-header">
                    <h3 class="lightbox-title" id="lightboxTitle"></h3>
                    <div class="lightbox-meta">
                        <span class="lightbox-category" id="lightboxCategory"></span>
                        <span class="lightbox-author" id="lightboxAuthor"></span>
                    </div>
                </div>
                <p class="lightbox-caption" id="lightboxCaption"></p>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <circle cx="12" cy="9" r="3" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    <span>EventMappr</span>
                </div>
                <div class="footer-links">
                    <a href="#">Privacy Policy</a>
                    <a href="#">Terms of Use</a>
                    <a href="#">Contact</a>
                    <a href="#">Community Forum</a>
                </div>
                <div class="footer-social">
                    <a href="#" class="social-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </a>
                    <a href="#" class="social-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </a>
                    <a href="#" class="social-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="currentColor" stroke-width="2"/>
                            <path d="m16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" stroke="currentColor" stroke-width="2"/>
                            <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 EventMappr. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="gallery.js"></script>
</body>
</html>