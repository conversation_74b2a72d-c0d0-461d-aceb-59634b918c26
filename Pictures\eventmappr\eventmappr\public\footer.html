<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>EventMappr</title>
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* Status bar */
        .status-bar {
            background: #000;
            color: white;
            padding: 8px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            font-weight: 600;
        }

        .status-left {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-right {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* Header */
        .header {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            color: white;
            font-size: 24px;
            font-weight: 300;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(45deg, #4a90e2, #7b68ee);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 4px;
        }

        /* Contributors badge */
        .contributors-section {
            text-align: center;
            padding: 40px 20px;
        }

        .contributors-badge {
            display: inline-flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 8px 16px;
            color: #ccc;
            font-size: 16px;
            gap: 8px;
        }

        .contributors-count {
            background: #7cb342;
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-weight: bold;
            font-size: 14px;
        }

        /* Main content */
        .main-content {
            background: rgba(30, 39, 73, 0.9);
            margin: 40px 0;
            padding: 40px 30px;
            border-radius: 0;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            max-width: 600px;
        }

        .brand-section h2 {
            color: white;
            font-size: 32px;
            font-weight: 300;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .brand-section .logo-icon {
            width: 40px;
            height: 40px;
            font-size: 20px;
        }

        .brand-description {
            color: #b0b0b0;
            font-size: 16px;
            line-height: 1.6;
        }

        .site-map h3 {
            color: white;
            font-size: 24px;
            font-weight: 400;
            margin-bottom: 8px;
            position: relative;
        }

        .site-map h3::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 40px;
            height: 3px;
            background: #4a90e2;
        }

        .site-map ul {
            list-style: none;
            margin-top: 20px;
        }

        .site-map li {
            margin-bottom: 16px;
        }

        .site-map a {
            color: #ccc;
            text-decoration: none;
            font-size: 16px;
            transition: color 0.3s ease;
        }

        .site-map a:hover {
            color: white;
        }

        /* Footer sections */
        .footer-sections {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            padding: 0 30px 40px;
        }

        .footer-section h4 {
            color: white;
            font-size: 20px;
            font-weight: 400;
            margin-bottom: 8px;
            position: relative;
        }

        .footer-section h4::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 30px;
            height: 3px;
            background: #4a90e2;
        }

        .legal-links {
            margin-top: 20px;
        }

        .legal-links a {
            display: block;
            color: #ccc;
            text-decoration: none;
            font-size: 16px;
            margin-bottom: 16px;
            transition: color 0.3s ease;
        }

        .legal-links a:hover {
            color: white;
        }

        .social-icons {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .social-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #ccc;
            text-decoration: none;
            font-size: 18px;
            transition: all 0.3s ease;
        }

        .social-icon:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        /* Copyright */
        .copyright {
            text-align: center;
            padding: 30px 20px;
            color: #888;
            font-size: 14px;
        }

        .made-with {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-top: 16px;
            color: #aaa;
            font-size: 14px;
        }

        .heart {
            color: #e74c3c;
            font-size: 16px;
        }

        /* Floating buttons */
        .floating-buttons {
            position: fixed;
            right: 20px;
            bottom: 100px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .floating-btn {
            width: 56px;
            height: 56px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: white;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }

        .floating-btn:hover {
            transform: translateY(-2px);
        }

        .scroll-up {
            background: #4a90e2;
        }

        .chat-btn {
            background: #4a90e2;
            font-size: 24px;
        }

        /* Bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #000;
            padding: 12px 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-btn {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            padding: 8px;
            cursor: pointer;
        }

        .nav-home {
            width: 32px;
            height: 32px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Responsive adjustments */
        @media (max-width: 600px) {
            .content-grid,
            .footer-sections {
                grid-template-columns: 1fr;
                gap: 40px;
            }
            
            .main-content {
                margin: 20px 0;
                padding: 30px 20px;
            }
            
            .brand-section h2 {
                font-size: 28px;
            }
        }
    </style>
    </head>
    <body>
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:07</span>
                <span>📱</span>
                <span>✈️</span>
            </div>
            <div class="status-right">
                <span>📶</span>
                <span>📶</span>
                <span>📶</span>
                <span>🔋 59%</span>
            </div>
        </div>

        <!-- Header -->
        <div class="header">
            <div class="logo">
                <div class="logo-icon">✦</div>
                <span>Event Mappr</span>
            </div>
            <button class="menu-btn">☰</button>
        </div>

        <!-- Contributors Section -->
        <div class="contributors-section">
            <div class="contributors-badge">
                <span>contributors</span>
                <span class="contributors-count">43</span>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="content-grid">
                <div class="brand-section">
                    <h2>
                        <div class="logo-icon">✦</div>
                        Event Mappr
                    </h2>
                    <div class="brand-description">
                        Discover and share local events happening in your
                        community.<br><br>
                        EventMappr helps you find and connect with events near
                        you.
                    </div>
                </div>

                <div class="site-map">
                    <h3>Site Map</h3>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#map">Map</a></li>
                        <li><a href="#contact">Contact</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#signin">Sign In</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Footer Sections -->
        <div class="footer-sections">
            <div class="footer-section">
                <h4>Legal</h4>
                <div class="legal-links">
                    <a href="#privacy">Privacy Policy</a>
                    <a href="#terms">Terms of Service</a>
                    <a href="#cookies">Cookie Policy</a>
                </div>
            </div>

            <div class="footer-section">
                <h4>Connect</h4>
                <div class="social-icons">
                    <a href="#" class="social-icon">𝕏</a>
                    <a href="#" class="social-icon">f</a>
                    <a href="#" class="social-icon">📷</a>
                    <a href="#" class="social-icon">🐙</a>
                </div>
            </div>
        </div>

        <!-- Copyright -->
        <div class="copyright">
            <div>© 2025 EventMappr. All rights reserved.</div>
            <div class="made-with">
                Made with <span class="heart">❤️</span> for local communities
            </div>
        </div>

        <!-- Floating Buttons -->
        <div class="floating-buttons">
            <button class="floating-btn scroll-up">↑</button>
            <button class="floating-btn chat-btn">💬</button>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <button class="nav-btn">←</button>
            <div class="nav-home">⚪</div>
            <button class="nav-btn">☰</button>
        </div>

        <script>
        // Smooth scroll to top
        document.querySelector('.scroll-up').addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Menu toggle functionality
        document.querySelector('.menu-btn').addEventListener('click', function() {
            alert('Menu clicked! Add your navigation logic here.');
        });

        // Chat button functionality
        document.querySelector('.chat-btn').addEventListener('click', function() {
            alert('Chat feature clicked! Integrate your chat system here.');
        });

        // Navigation functionality
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Add navigation logic here
                console.log('Navigation button clicked');
            });
        });

        // Link functionality
        document.querySelectorAll('a[href^="#"]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = this.getAttribute('href').substring(1);
                console.log('Navigate to:', target);
                // Add your routing logic here
            });
        });

        // Social media links
        document.querySelectorAll('.social-icon').forEach(icon => {
            icon.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Social media clicked');
                // Add your social media integration here
            });
        });
    </script>
    </body>
</html>