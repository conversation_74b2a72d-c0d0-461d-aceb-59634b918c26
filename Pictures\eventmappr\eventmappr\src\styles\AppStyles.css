/* App container styles */
.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Main content container styles */
.main-content {
  flex: 1;
  padding-top: 80px; /* Add padding to prevent navbar overlap */
  display: flex;
  flex-direction: column;
}

/* Tourist Places Section Modern Layout */
.tourist-places-container {
  display: flex;
  min-height: 70vh;
  background: #f8fafc;
  border-radius: 16px;
  box-shadow: 0 2px 16px 0 rgba(0,0,0,0.04);
  margin: 0 auto;
  max-width: 1200px;
  width: 100%;
}
.tp-sidebar {
  width: 340px;
  background: var(--tp-sidebar-bg, #fff);
  border-right: 1px solid var(--tp-border, #e5e7eb);
  padding: 32px 16px 16px 16px;
  overflow-y: auto;
  box-shadow: 2px 0 8px 0 rgba(0,0,0,0.03);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tp-list-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 0;
  margin: 0 auto;
  list-style: none;
  justify-content: center;
  align-items: center;
}
@media (max-width: 700px) {
  .tp-list-grid {
    grid-template-columns: 1fr;
  }
}

:root {
  --tp-card-bg: #fff;
  --tp-card-gradient: linear-gradient(90deg, #e0ecff 0%, #f8fafc 100%);
  --tp-card-border: #60a5fa;
  --tp-card-shadow: 0 2px 12px 0 rgba(56, 189, 248, 0.09);
  --tp-card-text: #1e293b;
  --tp-card-subtle: #64748b;
  --tp-badge-bg: #e0e7ef;
  --tp-badge-text: #2563eb;
  --tp-btn-bg: #e0e7ef;
  --tp-btn-text: #2563eb;
  --tp-sidebar-bg: #fff;
  --tp-border: #e5e7eb;
}
@media (prefers-color-scheme: dark) {
  :root {
    --tp-card-bg: #161b22;
    --tp-card-gradient: linear-gradient(90deg, #1e293b 0%, #334155 100%);
    --tp-card-border: #60a5fa;
    --tp-card-shadow: 0 2px 12px 0 rgba(56, 189, 248, 0.13);
    --tp-card-text: #f1f5f9;
    --tp-card-subtle: #cbd5e1;
    --tp-badge-bg: #1e293b;
    --tp-badge-text: #93c5fd;
    --tp-btn-bg: #1e293b;
    --tp-btn-text: #60a5fa;
    --tp-sidebar-bg: #0f172a;
    --tp-border: #334155;
  }
}

.tp-list-card {
  background: var(--tp-card-gradient);
  border-radius: 14px;
  box-shadow: var(--tp-card-shadow);
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 12px 10px;
  border: 1.5px solid var(--tp-card-border);
  border-left: 6px solid var(--tp-card-border);
  cursor: pointer;
  min-height: 78px;
  position: relative;
  transition: box-shadow 0.18s, border-color 0.18s, background 0.18s;
  color: var(--tp-card-text);
}
.tp-list-card.selected, .tp-list-card:hover {
  background: linear-gradient(90deg, #dbeafe 0%, #e0ecff 100%);
  box-shadow: 0 6px 24px 0 rgba(56, 189, 248, 0.15);
  border-color: #2563eb;
}
.tp-list-card h3 {
  font-size: 1.08rem;
  font-weight: 700;
  color: var(--tp-card-text);
  margin: 0 0 1px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tp-list-card p {
  font-size: 0.93rem;
  color: var(--tp-card-subtle);
  margin: 0;
}
.tp-list-card iframe {
  border-radius: 10px;
  box-shadow: 0 1px 6px 0 rgba(56,189,248,0.09);
  border: 1.5px solid var(--tp-card-border);
  background: #e5e7eb;
}
.tp-list-card .tp-badge {
  background: var(--tp-badge-bg);
  color: var(--tp-badge-text);
  font-size: 0.72rem;
  font-weight: 600;
  border-radius: 8px;
  padding: 2px 8px;
  margin-left: 2px;
  text-transform: capitalize;
}
.tp-list-card .tp-directions-btn {
  color: var(--tp-btn-text);
  background: var(--tp-btn-bg);
  border-radius: 6px;
  padding: 2px 7px;
  font-size: 0.89rem;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  font-weight: 500;
  gap: 3px;
  border: none;
  transition: background 0.18s, color 0.18s;
}
.tp-list-card .tp-directions-btn:hover {
  background: #bae6fd;
  color: #1d4ed8;
}
.tp-list-card .tp-map-pin {
  position: absolute;
  bottom: 4px;
  left: 4px;
  color: #2563eb;
  background: rgba(255,255,255,0.85);
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.10);
  pointer-events: none;
}

.tp-list-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 0;
  margin: 0;
  list-style: none;
}
@media (max-width: 700px) {
  .tp-list-grid {
    grid-template-columns: 1fr;
  }
}

.tp-list-card {
  background: linear-gradient(90deg, #e0ecff 0%, #f8fafc 100%);
  border-radius: 14px;
  box-shadow: 0 2px 12px 0 rgba(56, 189, 248, 0.09);
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 12px 10px;
  border: 1.5px solid #60a5fa;
  border-left: 6px solid #60a5fa;
  cursor: pointer;
  min-height: 78px;
  position: relative;
  transition: box-shadow 0.18s, border-color 0.18s, background 0.18s;
}
.tp-list-card.selected, .tp-list-card:hover {
  background: linear-gradient(90deg, #dbeafe 0%, #e0ecff 100%);
  box-shadow: 0 6px 24px 0 rgba(56, 189, 248, 0.15);
  border-color: #2563eb;
}
.tp-list-card h3 {
  font-size: 1.08rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tp-list-card p {
  font-size: 0.93rem;
  color: #64748b;
  margin: 0;
}
.tp-list-card iframe {
  border-radius: 10px;
  box-shadow: 0 1px 6px 0 rgba(56,189,248,0.09);
  border: 1.5px solid #60a5fa;
  background: #e5e7eb;
}
.tp-list-card .tp-badge {
  background: #e0e7ef;
  color: #2563eb;
  font-size: 0.72rem;
  font-weight: 600;
  border-radius: 8px;
  padding: 2px 8px;
  margin-left: 2px;
  text-transform: capitalize;
}
.tp-list-card .tp-directions-btn {
  color: #2563eb;
  background: #e0e7ef;
  border-radius: 6px;
  padding: 2px 7px;
  font-size: 0.89rem;
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  font-weight: 500;
  gap: 3px;
  border: none;
  transition: background 0.18s, color 0.18s;
}
.tp-list-card .tp-directions-btn:hover {
  background: #bae6fd;
  color: #1d4ed8;
}
.tp-list-card .tp-map-pin {
  position: absolute;
  bottom: 4px;
  left: 4px;
  color: #2563eb;
  background: rgba(255,255,255,0.85);
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.10);
  pointer-events: none;
}
.tp-list-card.selected, .tp-list-card:hover {
  background: #e0ecff;
  box-shadow: 0 4px 16px 0 rgba(56, 189, 248, 0.13);
  border-color: #60a5fa;
}
.tp-list-card h3 {
  font-size: 1.04rem;
  font-weight: 600;
  margin-bottom: 3px;
  color: #1e293b;
}
.tp-list-card p {
  font-size: 0.93rem;
  color: #64748b;
  margin: 0;
}
.tp-list-card iframe {
  border-radius: 9px;
  box-shadow: 0 1px 4px 0 rgba(0,0,0,0.08);
  border: 1px solid #e0e7ef;
  background: #e5e7eb;
}
.tp-list-card.selected, .tp-list-card:hover {
  background: #e0ecff;
  box-shadow: 0 2px 12px 0 rgba(56, 189, 248, 0.07);
  border-color: #60a5fa;
}
.tp-list-card h3 {
  font-size: 1.08rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #1e293b;
}
.tp-list-card p {
  font-size: 0.96rem;
  color: #64748b;
  margin: 0;
}
.tp-map-area {
  flex: 1;
  min-width: 0;
  height: 600px;
  background: #e5e7eb;
  border-radius: 0 16px 16px 0;
  overflow: hidden;
  position: relative;
}

@media (max-width: 900px) {
  .tourist-places-container {
    flex-direction: column;
    max-width: 100vw;
    border-radius: 0;
  }
  .tp-sidebar {
    width: 100%;
    min-height: 200px;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
    border-radius: 0;
    padding: 16px 8px;
    box-shadow: none;
  }
  .tp-map-area {
    border-radius: 0 0 16px 16px;
    height: 350px;
  }
}

@media (max-width: 600px) {
  .tp-list-card {
    padding: 12px 10px;
    font-size: 0.95rem;
  }
  .tp-map-area {
    height: 220px;
  }
}


/* Home container styles */
.home-container {
  flex: 1;
}

/* Navbar adjustments */
.navbar {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1000;
}

/* Fix for hero section to avoid overlap with navbar */
.hero {
  padding-top: 2rem;
  margin-top: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .home-container {
    padding-top: 70px;
  }
  
  .hero {
    padding-top: 1rem;
  }
}

/* Fix for footer duplication */
footer {
  margin-top: auto; /* Push footer to the bottom */
} 