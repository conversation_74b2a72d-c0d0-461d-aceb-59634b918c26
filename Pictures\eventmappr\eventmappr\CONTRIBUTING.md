---

# 📍 Contributing to EventMappr

Welcome to **EventMappr** — a collaborative platform designed to map, manage, and explore events with ease. Whether you're here to squash bugs, refine the UI, enhance backend logic, improve documentation, or contribute new ideas — we’re **excited to collaborate with you!** 🥳

This guide will walk you through the contribution process and help you get started smoothly.

---

## 📜 Code of Conduct

Please be respectful and inclusive. By participating, you agree to follow our [Code of Conduct](CODE_OF_CONDUCT.md).

---

## 🛠️ How You Can Contribute

We welcome a variety of contributions:

* 🐛 Bug reports and fixes
* 💡 Feature suggestions and implementations
* 🎨 UI/UX improvements
* 📘 Documentation enhancements
* 🧪 Adding or improving tests
* 🔍 Reviewing pull requests

No contribution is too small — **every bit counts!**

---

## 🚀 Getting Started

### 1️⃣ Fork the Repository

Click the **Fork** button (top right of this page) to make your copy.

### 2️⃣ Clone Your Fork

```bash
git clone https://github.com/<your-username>/eventmappr.git
cd eventmappr
```

### 3️⃣ Create a New Branch

Use a descriptive branch name:

```bash
git checkout -b fix/map-bug
```

### 4️⃣ Make Changes

Edit, build, and test your changes locally. Use the project structure as a guide (see below).

### 5️⃣ Stage and Commit

```bash
git add .
git commit -m "fix: resolve map rendering issue on mobile"
```

### 6️⃣ Push and Create PR

```bash
git push origin fix/map-bug
```

Then go to GitHub, click **Compare & pull request**, and describe your changes clearly.

---

## 💡 Best Practices

* ✅ **Write atomic commits** – Keep each commit focused on one thing.
* ✅ **Use clear messages** – e.g., `feat: add event filter by category`.
* ✅ **Add comments** – Make your code easy to understand.
* ✅ **Respect file structure** – Keep code organized.
* ❌ Avoid submitting multiple unrelated changes in one PR.

---

## 🧪 Testing Your Changes

Before opening a PR:

* Run your code and ensure it works across common browsers/devices.
* Confirm that existing features aren't broken.
* If applicable, add or update unit tests.
* Manually test frontend changes.

---

## 🐛 Reporting Bugs

If you found a bug:

1. [Open a new issue](https://github.com/Bhavya1352/eventmappr/issues/new).
2. Include clear steps to reproduce, screenshots, logs, and browser info.
3. Use appropriate labels like `bug`, `enhancement`, `question`.

⚠️ **Important**: Wait for an issue to be assigned to you before starting work on it.

---

## 🙌 First-Time Contributors

Welcome! Here are a few resources to help you get started:

* [GitHub Forking Guide](https://docs.github.com/en/get-started/quickstart/fork-a-repo)
* [How to Make a Pull Request](https://opensource.com/article/19/7/create-pull-request-github)
* [Git Cheat Sheet](https://education.github.com/git-cheat-sheet-education.pdf)

Have questions? Open an issue and tag it with `help wanted`.

---

## ✅ Contribution Checklist

Before submitting your PR, make sure:

* [ ] Code compiles and runs locally
* [ ] You followed existing style conventions
* [ ] Changes are documented where needed
* [ ] The PR has a clear title and description
* [ ] You linked a related issue (if applicable)
* [ ] You've been assigned the issue (for tracked tasks)

---

## 🙏 A Note from the Maintainers

Thank you for contributing to **EventMappr**! This platform exists because of your support and collaboration. Please avoid low-effort PRs during events like Hacktoberfest — quality matters more than quantity.

Let’s map the world of events — one contribution at a time. 🌐📍

---

<div align="center">
  <img src="https://media.giphy.com/media/3o7aD2saalBwwftBIY/giphy.gif" width="100" />
  <br />
  <em><b>Happy contributing! The map is waiting for your mark!</b></em>
</div>

---
