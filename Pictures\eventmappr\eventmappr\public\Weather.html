<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Planner - EventMappr</title>
    <link rel="stylesheet" href="Weather.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />

</head>

<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-map-marker-alt"></i>
                <span>EventMappr</span>
            </div>
            <div class="nav-links">
                <a href="Home.html" class="nav-link">HOME</a>
                <a href="#" class="nav-link weather-link">
                    <i class="fas fa-calendar-alt"></i> Weather Planner
                </a>
                <a href="#" class="nav-link">ABOUT US</a>
                <button class="btn-secondary" onclick="location.href='Badge.html'">GENERATE BADGE</button>
                <button class="btn-primary" onclick="location.href='index.html'">ADD EVENT</button>
            </div>
            <div class="mobile-menu-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <h1 class="hero-title">Weather Planner</h1>
                <p class="hero-subtitle">Plan perfect events with real-time weather forecasts and smart recommendations
                </p>

                <!-- Search Section -->
                <div class="search-section">
                    <div class="search-container">
                        <input type="text" id="cityInput" placeholder="Enter city name (e.g., London, New York)"
                            class="city-input">
                        <button id="searchBtn" class="search-btn">
                            <span class="search-icon">🔍</span>
                            Get Forecast
                        </button>
                    </div>
                    <div id="loadingState" class="loading-state hidden">
                        <div class="loading-spinner"></div>
                        <span>Fetching weather data...</span>
                    </div>
                    <div id="errorState" class="error-state hidden">
                        <span class="error-icon">⚠️</span>
                        <span id="errorMessage"></span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Weather Results Section -->
        <section id="weatherResults" class="weather-results hidden">
            <div class="results-container">
                <!-- Current Weather Card -->
                <div class="current-weather-card">
                    <div class="current-weather-header">
                        <h2 id="currentLocation">Current Weather</h2>
                        <span id="currentDate"></span>
                    </div>
                    <div class="current-weather-content">
                        <div class="current-temp">
                            <span id="currentIcon" class="weather-icon-large"></span>
                            <div class="temp-info">
                                <span id="currentTemp" class="temperature">--°</span>
                                <span id="currentCondition" class="condition">Loading...</span>
                            </div>
                        </div>
                        <div class="current-details">
                            <div class="detail-item">
                                <span class="detail-label">Feels like</span>
                                <span id="feelsLike" class="detail-value">--°</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Humidity</span>
                                <span id="humidity" class="detail-value">--%</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Wind Speed</span>
                                <span id="windSpeed" class="detail-value">-- km/h</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Event Recommendation -->
                <div id="eventRecommendation" class="event-recommendation">
                    <div class="recommendation-header">
                        <span class="recommendation-icon">🎯</span>
                        <h3>Event Planning Recommendation</h3>
                    </div>
                    <div id="recommendationContent" class="recommendation-content">
                        <p id="bestDay">Analyzing weather patterns...</p>
                        <p id="eventTips">Loading recommendations...</p>
                    </div>
                </div>

                <!-- 5-Day Forecast -->
                <div class="forecast-section">
                    <h3 class="forecast-title">5-Day Forecast</h3>
                    <div id="forecastContainer" class="forecast-container">
                        <!-- Forecast cards will be generated here -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-brand">
                <div class="nav-logo">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>EventMappr</span>
                </div>
            </div>
            <div class="footer-links">
                <a href="#" class="footer-link">Privacy Policy</a>
                <a href="#" class="footer-link">Terms of Use</a>
                <a href="#" class="footer-link">Contact</a>
                <a href="#" class="footer-link">Community Forum</a>
                <a href="Weather.html" class="footer-link">
                    <i class="fas fa-calendar-alt" style="margin-right: 6px; color: #f1c40f;"></i> Weather Planner
                </a>
            </div>
            <div class="footer-social">
                <a href="#" class="social-link" aria-label="GitHub">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z" />
                    </svg>
                </a>
                <a href="#" class="social-link" aria-label="Twitter">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z" />
                    </svg>
                </a>
                <a href="#" class="social-link" aria-label="Instagram">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path
                            d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z" />
                    </svg>
                </a>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 EventMappr. All rights reserved.</p>
        </div>
    </footer>

    <script src="Weather.js"></script>
</body>

</html>