# 📦 Pull Request: Eventmappr Contribution

## ✅ Description

Please describe what this PR does and link any related issues.

Fixes: #[issue-number]

## 📂 Type of Change

- [ ] Bug Fix 🐛
- [ ] New Feature ✨
- [ ] Documentation 📚
- [ ] UI/UX Enhancement 🎨
- [ ] Refactor 🧹
- [ ] Other:

## 📋 Checklist

- [ ] My code follows the contribution guidelines of Eventmappr
- [ ] I have tested my changes locally
- [ ] I have updated relevant documentation
- [ ] I have linked related issues (if any)
- [ ] This pull request is ready to be reviewed

## 🎥 Screenshots or Demo (if applicable)

Include screenshots or screen recordings of your change.
